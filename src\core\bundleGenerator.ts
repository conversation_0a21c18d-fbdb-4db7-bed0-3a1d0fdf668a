/**
 * Bundle generator for creating downloadable ZIP files
 */

import * as fs from 'fs-extra';
import * as path from 'path';
import archiver from 'archiver';
import { v4 as uuidv4 } from 'uuid';
import { MCPManifest, ServerConfig, ServerGenerationError } from '../types';
import { ensureDir, formatBytes } from '../utils';

export interface BundleContent {
  manifest: MCPManifest;
  files: Record<string, string>;
  config: ServerConfig;
}

export class BundleGenerator {
  private bundleDir = '/tmp/mcp-bundles';

  constructor() {
    // Ensure bundle directory exists
    this.ensureBundleDir();
  }

  /**
   * Create a ZIP bundle with all generated files
   */
  async createBundle(bundleId: string, content: BundleContent): Promise<string> {
    try {
      const bundlePath = path.join(this.bundleDir, `${bundleId}.zip`);
      const tempDir = path.join(this.bundleDir, bundleId);

      // Remove existing bundle if it exists (force overwrite)
      try {
        await fs.remove(bundlePath);
      } catch {
        // Ignore if file doesn't exist
      }

      // Remove existing temp directory if it exists
      try {
        await fs.remove(tempDir);
      } catch {
        // Ignore if directory doesn't exist
      }

      // Create temporary directory for files
      await ensureDir(tempDir);

      // Write all files to temporary directory
      await this.writeFilesToTemp(tempDir, content);

      // Create ZIP archive
      await this.createZipArchive(tempDir, bundlePath);

      // Clean up temporary directory
      await fs.remove(tempDir);

      return bundlePath;
    } catch (error) {
      throw new ServerGenerationError(
        `Failed to create bundle: ${bundleId}`,
        error as Error
      );
    }
  }

  /**
   * Write all generated files to temporary directory
   */
  private async writeFilesToTemp(tempDir: string, content: BundleContent): Promise<void> {
    const { manifest, files } = content;

    // Write MCP manifest
    await fs.writeFile(
      path.join(tempDir, 'mcp.json'), 
      JSON.stringify(manifest, null, 2)
    );

    // Write all generated files
    for (const [filePath, fileContent] of Object.entries(files)) {
      const fullPath = path.join(tempDir, filePath);
      
      // Ensure directory exists
      await ensureDir(path.dirname(fullPath));
      
      // Write file
      await fs.writeFile(fullPath, fileContent);
    }

    // Write additional metadata
    await this.writeMetadata(tempDir, content);
  }

  /**
   * Write bundle metadata
   */
  private async writeMetadata(tempDir: string, content: BundleContent): Promise<void> {
    const metadata = {
      generatedAt: new Date().toISOString(),
      generator: 'openapi-to-mcp',
      version: '1.0.0',
      config: content.config,
      files: Object.keys(content.files),
      toolCount: content.manifest.tools.length
    };

    await fs.writeFile(
      path.join(tempDir, '.metadata.json'),
      JSON.stringify(metadata, null, 2)
    );

    // Create installation instructions
    const instructions = this.generateInstallationInstructions(content.config);
    await fs.writeFile(path.join(tempDir, 'INSTALLATION.md'), instructions);
  }

  /**
   * Generate installation instructions
   */
  private generateInstallationInstructions(config: ServerConfig): string {
    return `# Installation Instructions

## Quick Start

1. **Extract the bundle**:
   \`\`\`bash
   unzip ${config.name}.zip
   cd ${config.name}
   \`\`\`

2. **Install dependencies**:
   \`\`\`bash
   npm install
   \`\`\`

3. **Build the project**:
   \`\`\`bash
   npm run build
   \`\`\`

4. **Configure environment** (optional):
   \`\`\`bash
   cp .env.example .env
   # Edit .env file with your settings
   \`\`\`

5. **Start the server**:
   \`\`\`bash
   npm start
   \`\`\`

## Configuration

- **Port**: ${config.port} (configurable via PORT environment variable)
- **Base URL**: ${config.baseUrl}
- **Name**: ${config.name}
- **Version**: ${config.version}

## Testing

Once the server is running, test the health endpoint:
\`\`\`bash
curl http://localhost:${config.port}/health
\`\`\`

## MCP Protocol

Send requests to the MCP endpoint:
\`\`\`bash
curl -X POST http://localhost:${config.port}/mcp \\
  -H "Content-Type: application/json" \\
  -d '{
    "tool": "toolName",
    "parameters": { "param1": "value1" }
  }'
\`\`\`

## Generated Files

- \`src/server.ts\` - Main server application
- \`src/routes.ts\` - API route handlers
- \`src/types.ts\` - TypeScript type definitions
- \`mcp.json\` - MCP manifest file
- \`package.json\` - Node.js dependencies
- \`tsconfig.json\` - TypeScript configuration

For more information, see README.md.
`;
  }

  /**
   * Create ZIP archive from directory
   */
  private async createZipArchive(sourceDir: string, outputPath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const output = fs.createWriteStream(outputPath);
      const archive = archiver('zip', {
        zlib: { level: 9 } // Maximum compression
      });

      output.on('close', () => {
        resolve();
      });

      archive.on('error', (error) => {
        reject(error);
      });

      archive.pipe(output);
      archive.directory(sourceDir, false);
      archive.finalize();
    });
  }

  /**
   * Check if bundle exists
   */
  async bundleExists(bundleId: string): Promise<boolean> {
    const bundlePath = path.join(this.bundleDir, `${bundleId}.zip`);
    try {
      await fs.access(bundlePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get bundle file path
   */
  async getBundlePath(bundleId: string): Promise<string> {
    const bundlePath = path.join(this.bundleDir, `${bundleId}.zip`);
    
    if (!(await this.bundleExists(bundleId))) {
      throw new Error(`Bundle ${bundleId} not found`);
    }
    
    return bundlePath;
  }

  /**
   * Get bundle file size
   */
  async getBundleSize(bundlePath: string): Promise<string> {
    try {
      const stats = await fs.stat(bundlePath);
      return formatBytes(stats.size);
    } catch {
      return 'Unknown';
    }
  }

  /**
   * Delete bundle file
   */
  async deleteBundle(bundleId: string): Promise<boolean> {
    try {
      const bundlePath = path.join(this.bundleDir, `${bundleId}.zip`);
      await fs.remove(bundlePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * List all bundles
   */
  async listBundles(): Promise<Array<{ id: string; path: string; size: string; created: Date }>> {
    try {
      const files = await fs.readdir(this.bundleDir);
      const bundles = [];

      for (const file of files) {
        if (file.endsWith('.zip')) {
          const bundleId = path.basename(file, '.zip');
          const bundlePath = path.join(this.bundleDir, file);
          const stats = await fs.stat(bundlePath);
          
          bundles.push({
            id: bundleId,
            path: bundlePath,
            size: formatBytes(stats.size),
            created: stats.birthtime
          });
        }
      }

      return bundles.sort((a, b) => b.created.getTime() - a.created.getTime());
    } catch {
      return [];
    }
  }

  /**
   * Clean up old bundles (older than specified hours)
   */
  async cleanupOldBundles(maxAgeHours: number = 24): Promise<number> {
    try {
      const bundles = await this.listBundles();
      const cutoffTime = new Date(Date.now() - maxAgeHours * 60 * 60 * 1000);
      let deletedCount = 0;

      for (const bundle of bundles) {
        if (bundle.created < cutoffTime) {
          if (await this.deleteBundle(bundle.id)) {
            deletedCount++;
          }
        }
      }

      return deletedCount;
    } catch {
      return 0;
    }
  }

  /**
   * Get total storage usage
   */
  async getStorageUsage(): Promise<{ totalFiles: number; totalSize: string }> {
    try {
      const bundles = await this.listBundles();
      let totalBytes = 0;

      for (const bundle of bundles) {
        const stats = await fs.stat(bundle.path);
        totalBytes += stats.size;
      }

      return {
        totalFiles: bundles.length,
        totalSize: formatBytes(totalBytes)
      };
    } catch {
      return {
        totalFiles: 0,
        totalSize: '0 Bytes'
      };
    }
  }

  /**
   * Ensure bundle directory exists
   */
  private async ensureBundleDir(): Promise<void> {
    try {
      await ensureDir(this.bundleDir);
    } catch (error) {
      console.warn('Could not create bundle directory:', error);
      // Fallback to current directory
      this.bundleDir = path.join(process.cwd(), 'tmp', 'mcp-bundles');
      await ensureDir(this.bundleDir);
    }
  }

  /**
   * Create bundle stream for direct download without saving to disk
   */
  async createBundleStream(content: BundleContent): Promise<NodeJS.ReadableStream> {
    const tempDir = path.join(this.bundleDir, `temp-${uuidv4()}`);
    
    try {
      // Create temporary files
      await this.writeFilesToTemp(tempDir, content);

      // Create archive stream
      const archive = archiver('zip', { zlib: { level: 9 } });
      
      // Add all files to archive
      archive.directory(tempDir, false);
      archive.finalize();

      // Clean up temp directory after stream ends
      archive.on('end', async () => {
        try {
          await fs.remove(tempDir);
        } catch (error) {
          console.warn('Failed to clean up temp directory:', tempDir, error);
        }
      });

      return archive;
    } catch (error) {
      // Clean up on error
      try {
        await fs.remove(tempDir);
      } catch {}
      throw error;
    }
  }
}

export default BundleGenerator;
