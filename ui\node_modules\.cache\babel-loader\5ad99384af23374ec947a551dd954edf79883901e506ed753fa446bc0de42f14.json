{"ast": null, "code": "import _objectSpread from \"D:/repos-personal/repos/openapi-to-mcp/ui/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"D:/repos-personal/repos/openapi-to-mcp/ui/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"color\", \"size\", \"strokeWidth\", \"absoluteStrokeWidth\", \"children\"];\n/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport { forwardRef, createElement } from 'react';\nimport defaultAttributes from './defaultAttributes.mjs';\nconst toKebabCase = string => string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase();\nconst createLucideIcon = (iconName, iconNode) => {\n  const Component = forwardRef((_ref, ref) => {\n    let {\n        color = \"currentColor\",\n        size = 24,\n        strokeWidth = 2,\n        absoluteStrokeWidth,\n        children\n      } = _ref,\n      rest = _objectWithoutProperties(_ref, _excluded);\n    return createElement(\"svg\", _objectSpread(_objectSpread({\n      ref\n    }, defaultAttributes), {}, {\n      width: size,\n      height: size,\n      stroke: color,\n      strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n      className: \"lucide lucide-\".concat(toKebabCase(iconName))\n    }, rest), [...iconNode.map(_ref2 => {\n      let [tag, attrs] = _ref2;\n      return createElement(tag, attrs);\n    }), ...((Array.isArray(children) ? children : [children]) || [])]);\n  });\n  Component.displayName = \"\".concat(iconName);\n  return Component;\n};\nvar createLucideIcon$1 = createLucideIcon;\nexport { createLucideIcon$1 as default, toKebabCase };", "map": {"version": 3, "names": ["toKebabCase", "string", "replace", "toLowerCase", "createLucideIcon", "iconName", "iconNode", "Component", "forwardRef", "_ref", "ref", "color", "size", "strokeWidth", "absoluteStrokeWidth", "children", "rest", "_objectWithoutProperties", "_excluded", "createElement", "_objectSpread", "defaultAttributes", "width", "height", "stroke", "Number", "className", "concat", "map", "_ref2", "tag", "attrs", "Array", "isArray", "displayName", "createLucideIcon$1"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\createLucideIcon.ts"], "sourcesContent": ["import { forwardRef, createElement, ReactSVG, SVGProps, ForwardRefExoticComponent, RefAttributes } from 'react';\nimport defaultAttributes from './defaultAttributes';\n\nexport type IconNode = [elementName: keyof ReactSVG, attrs: Record<string, string>][]\n\nexport type SVGAttributes = Partial<SVGProps<SVGSVGElement>>\ntype ComponentAttributes = RefAttributes<SVGSVGElement> & SVGAttributes\n\nexport interface LucideProps extends ComponentAttributes {\n  size?: string | number\n  absoluteStrokeWidth?: boolean\n}\n\nexport type LucideIcon = ForwardRefExoticComponent<LucideProps>;\n/**\n * Converts string to KebabCase\n * Copied from scripts/helper. If anyone knows how to properly import it here\n * then please fix it.\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) => string.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n\nconst createLucideIcon = (iconName: string, iconNode: IconNode): LucideIcon => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(\n    ({ color = 'currentColor', size = 24, strokeWidth = 2, absoluteStrokeWidth, children, ...rest }, ref) =>\n      createElement(\n        'svg',\n        {\n          ref,\n          ...defaultAttributes,\n          width: size,\n          height: size,\n          stroke: color,\n          strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n          className: `lucide lucide-${toKebabCase(iconName)}`,\n          ...rest,\n        },\n        [\n          ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n          ...(\n            (Array.isArray(children) ? children : [children]) || []\n          )\n        ],\n      ),\n  );\n\n  Component.displayName = `${iconName}`;\n\n  return Component;\n};\n\nexport default createLucideIcon\n"], "mappings": ";;;;;;;;;AAsBa,MAAAA,WAAA,GAAeC,MAAmB,IAAAA,MAAA,CAAOC,OAAA,CAAQ,oBAAsB,SAAO,EAAEC,WAAY;AAEzG,MAAMC,gBAAA,GAAmBA,CAACC,QAAA,EAAkBC,QAAmC;EAC7E,MAAMC,SAAY,GAAAC,UAAA,CAChB,CAAAC,IAAA,EAAiGC,GAC/F;IAAA,IADD;QAAEC,KAAQ;QAAgBC,IAAO;QAAIC,WAAc;QAAGC,mBAAqB;QAAAC;MAAkB,IAAAN,IAAA;MAALO,IAAK,GAAAC,wBAAA,CAAAR,IAAA,EAAAS,SAAA;IAAA,OAC5FC,aAAA,CACE,OAAAC,aAAA,CAAAA,aAAA;MAEEV;IAAA,GACGW,iBAAA;MACHC,KAAO,EAAAV,IAAA;MACPW,MAAQ,EAAAX,IAAA;MACRY,MAAQ,EAAAb,KAAA;MACRE,WAAA,EAAaC,mBAAA,GAAsBW,MAAO,CAAAZ,WAAW,IAAI,EAAK,GAAAY,MAAA,CAAOb,IAAI,CAAI,GAAAC,WAAA;MAC7Ea,SAAA,mBAAAC,MAAA,CAA4B3B,WAAA,CAAYK,QAAQ;IAAA,GAC7CW,IAAA,GAEL,CACE,GAAGV,QAAS,CAAAsB,GAAA,CAAIC,KAAA;MAAA,IAAC,CAACC,GAAK,EAAAC,KAAK,CAAM,GAAAF,KAAA;MAAA,OAAAV,aAAA,CAAcW,GAAK,EAAAC,KAAK,CAAC;IAAA,IAC3D,KACGC,KAAA,CAAMC,OAAQ,CAAAlB,QAAQ,IAAIA,QAAW,IAACA,QAAQ,MAAM,EAAC,EAG5D;EAAA,EACJ;EAEAR,SAAA,CAAU2B,WAAA,MAAAP,MAAA,CAAiBtB,QAAA;EAEpB,OAAAE,SAAA;AACT;AAEA,IAAA4B,kBAAA,GAAe/B,gBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}