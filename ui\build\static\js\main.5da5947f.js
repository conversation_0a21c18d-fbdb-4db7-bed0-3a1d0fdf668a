/*! For license information please see main.5da5947f.js.LICENSE.txt */
(()=>{"use strict";var e={43:(e,t,n)=>{e.exports=n(202)},153:(e,t,n)=>{var r=n(43),a=Symbol.for("react.element"),l=Symbol.for("react.fragment"),o=Object.prototype.hasOwnProperty,i=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,n){var r,l={},u=null,c=null;for(r in void 0!==n&&(u=""+n),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(c=t.ref),t)o.call(t,r)&&!s.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===l[r]&&(l[r]=t[r]);return{$$typeof:a,type:e,key:u,ref:c,props:l,_owner:i.current}}t.jsx=u,t.jsxs=u},202:(e,t)=>{var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),i=Symbol.for("react.provider"),s=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var m={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},h=Object.assign,g={};function v(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||m}function y(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||m}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=v.prototype;var x=b.prototype=new y;x.constructor=b,h(x,v.prototype),x.isPureReactComponent=!0;var w=Array.isArray,k=Object.prototype.hasOwnProperty,S={current:null},N={key:!0,ref:!0,__self:!0,__source:!0};function C(e,t,r){var a,l={},o=null,i=null;if(null!=t)for(a in void 0!==t.ref&&(i=t.ref),void 0!==t.key&&(o=""+t.key),t)k.call(t,a)&&!N.hasOwnProperty(a)&&(l[a]=t[a]);var s=arguments.length-2;if(1===s)l.children=r;else if(1<s){for(var u=Array(s),c=0;c<s;c++)u[c]=arguments[c+2];l.children=u}if(e&&e.defaultProps)for(a in s=e.defaultProps)void 0===l[a]&&(l[a]=s[a]);return{$$typeof:n,type:e,key:o,ref:i,props:l,_owner:S.current}}function E(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var j=/\/+/g;function P(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function _(e,t,a,l,o){var i=typeof e;"undefined"!==i&&"boolean"!==i||(e=null);var s=!1;if(null===e)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case n:case r:s=!0}}if(s)return o=o(s=e),e=""===l?"."+P(s,0):l,w(o)?(a="",null!=e&&(a=e.replace(j,"$&/")+"/"),_(o,t,a,"",(function(e){return e}))):null!=o&&(E(o)&&(o=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(o,a+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(j,"$&/")+"/")+e)),t.push(o)),1;if(s=0,l=""===l?".":l+":",w(e))for(var u=0;u<e.length;u++){var c=l+P(i=e[u],u);s+=_(i,t,a,c,o)}else if(c=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=p&&e[p]||e["@@iterator"])?e:null}(e),"function"===typeof c)for(e=c.call(e),u=0;!(i=e.next()).done;)s+=_(i=i.value,t,a,c=l+P(i,u++),o);else if("object"===i)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function z(e,t,n){if(null==e)return e;var r=[],a=0;return _(e,r,"","",(function(e){return t.call(n,e,a++)})),r}function O(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var T={current:null},M={transition:null},L={ReactCurrentDispatcher:T,ReactCurrentBatchConfig:M,ReactCurrentOwner:S};function R(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:z,forEach:function(e,t,n){z(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return z(e,(function(){t++})),t},toArray:function(e){return z(e,(function(e){return e}))||[]},only:function(e){if(!E(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=v,t.Fragment=a,t.Profiler=o,t.PureComponent=b,t.StrictMode=l,t.Suspense=c,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=L,t.act=R,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=h({},e.props),l=e.key,o=e.ref,i=e._owner;if(null!=t){if(void 0!==t.ref&&(o=t.ref,i=S.current),void 0!==t.key&&(l=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(u in t)k.call(t,u)&&!N.hasOwnProperty(u)&&(a[u]=void 0===t[u]&&void 0!==s?s[u]:t[u])}var u=arguments.length-2;if(1===u)a.children=r;else if(1<u){s=Array(u);for(var c=0;c<u;c++)s[c]=arguments[c+2];a.children=s}return{$$typeof:n,type:e.type,key:l,ref:o,props:a,_owner:i}},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:i,_context:e},e.Consumer=e},t.createElement=C,t.createFactory=function(e){var t=C.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=E,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:O}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=M.transition;M.transition={};try{e()}finally{M.transition=t}},t.unstable_act=R,t.useCallback=function(e,t){return T.current.useCallback(e,t)},t.useContext=function(e){return T.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return T.current.useDeferredValue(e)},t.useEffect=function(e,t){return T.current.useEffect(e,t)},t.useId=function(){return T.current.useId()},t.useImperativeHandle=function(e,t,n){return T.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return T.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return T.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return T.current.useMemo(e,t)},t.useReducer=function(e,t,n){return T.current.useReducer(e,t,n)},t.useRef=function(e){return T.current.useRef(e)},t.useState=function(e){return T.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return T.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return T.current.useTransition()},t.version="18.3.1"},234:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<l(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,o=a>>>1;r<o;){var i=2*(r+1)-1,s=e[i],u=i+1,c=e[u];if(0>l(s,n))u<a&&0>l(c,s)?(e[r]=c,e[u]=n,r=u):(e[r]=s,e[i]=n,r=i);else{if(!(u<a&&0>l(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function l(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var o=performance;t.unstable_now=function(){return o.now()}}else{var i=Date,s=i.now();t.unstable_now=function(){return i.now()-s}}var u=[],c=[],d=1,f=null,p=3,m=!1,h=!1,g=!1,v="function"===typeof setTimeout?setTimeout:null,y="function"===typeof clearTimeout?clearTimeout:null,b="undefined"!==typeof setImmediate?setImmediate:null;function x(e){for(var t=r(c);null!==t;){if(null===t.callback)a(c);else{if(!(t.startTime<=e))break;a(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function w(e){if(g=!1,x(e),!h)if(null!==r(u))h=!0,M(k);else{var t=r(c);null!==t&&L(w,t.startTime-e)}}function k(e,n){h=!1,g&&(g=!1,y(E),E=-1),m=!0;var l=p;try{for(x(n),f=r(u);null!==f&&(!(f.expirationTime>n)||e&&!_());){var o=f.callback;if("function"===typeof o){f.callback=null,p=f.priorityLevel;var i=o(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof i?f.callback=i:f===r(u)&&a(u),x(n)}else a(u);f=r(u)}if(null!==f)var s=!0;else{var d=r(c);null!==d&&L(w,d.startTime-n),s=!1}return s}finally{f=null,p=l,m=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var S,N=!1,C=null,E=-1,j=5,P=-1;function _(){return!(t.unstable_now()-P<j)}function z(){if(null!==C){var e=t.unstable_now();P=e;var n=!0;try{n=C(!0,e)}finally{n?S():(N=!1,C=null)}}else N=!1}if("function"===typeof b)S=function(){b(z)};else if("undefined"!==typeof MessageChannel){var O=new MessageChannel,T=O.port2;O.port1.onmessage=z,S=function(){T.postMessage(null)}}else S=function(){v(z,0)};function M(e){C=e,N||(N=!0,S())}function L(e,n){E=v((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){h||m||(h=!0,M(k))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):j=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,a,l){var o=t.unstable_now();switch("object"===typeof l&&null!==l?l="number"===typeof(l=l.delay)&&0<l?o+l:o:l=o,e){case 1:var i=-1;break;case 2:i=250;break;case 5:i=1073741823;break;case 4:i=1e4;break;default:i=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:l,expirationTime:i=l+i,sortIndex:-1},l>o?(e.sortIndex=l,n(c,e),null===r(u)&&e===r(c)&&(g?(y(E),E=-1):g=!0,L(w,l-o))):(e.sortIndex=i,n(u,e),h||m||(h=!0,M(k))),e},t.unstable_shouldYield=_,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},391:(e,t,n)=>{var r=n(950);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},579:(e,t,n)=>{e.exports=n(153)},730:(e,t,n)=>{var r=n(43),a=n(853);function l(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var o=new Set,i={};function s(e,t){u(e,t),u(e+"Capture",t)}function u(e,t){for(i[e]=t,e=0;e<t.length;e++)o.add(t[e])}var c=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,p={},m={};function h(e,t,n,r,a,l,o){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=l,this.removeEmptyString=o}var g={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){g[e]=new h(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];g[t]=new h(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){g[e]=new h(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){g[e]=new h(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){g[e]=new h(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){g[e]=new h(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){g[e]=new h(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){g[e]=new h(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){g[e]=new h(e,5,!1,e.toLowerCase(),null,!1,!1)}));var v=/[\-:]([a-z])/g;function y(e){return e[1].toUpperCase()}function b(e,t,n,r){var a=g.hasOwnProperty(t)?g[t]:null;(null!==a?0!==a.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,a,r)&&(n=null),r||null===a?function(e){return!!d.call(m,e)||!d.call(p,e)&&(f.test(e)?m[e]=!0:(p[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=null===n?3!==a.type&&"":n:(t=a.attributeName,r=a.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(a=a.type)||4===a&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(v,y);g[t]=new h(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(v,y);g[t]=new h(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(v,y);g[t]=new h(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){g[e]=new h(e,1,!1,e.toLowerCase(),null,!1,!1)})),g.xlinkHref=new h("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){g[e]=new h(e,1,!1,e.toLowerCase(),null,!0,!0)}));var x=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,w=Symbol.for("react.element"),k=Symbol.for("react.portal"),S=Symbol.for("react.fragment"),N=Symbol.for("react.strict_mode"),C=Symbol.for("react.profiler"),E=Symbol.for("react.provider"),j=Symbol.for("react.context"),P=Symbol.for("react.forward_ref"),_=Symbol.for("react.suspense"),z=Symbol.for("react.suspense_list"),O=Symbol.for("react.memo"),T=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var M=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var L=Symbol.iterator;function R(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=L&&e[L]||e["@@iterator"])?e:null}var I,D=Object.assign;function F(e){if(void 0===I)try{throw Error()}catch(Ve){var t=Ve.stack.trim().match(/\n( *(at )?)/);I=t&&t[1]||""}return"\n"+I+e}var U=!1;function A(e,t){if(!e||U)return"";U=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(Ae){var r=Ae}Reflect.construct(e,[],t)}else{try{t.call()}catch(Ae){r=Ae}e.call(t.prototype)}else{try{throw Error()}catch(Ae){r=Ae}e()}}catch(Ae){if(Ae&&r&&"string"===typeof Ae.stack){for(var a=Ae.stack.split("\n"),l=r.stack.split("\n"),o=a.length-1,i=l.length-1;1<=o&&0<=i&&a[o]!==l[i];)i--;for(;1<=o&&0<=i;o--,i--)if(a[o]!==l[i]){if(1!==o||1!==i)do{if(o--,0>--i||a[o]!==l[i]){var s="\n"+a[o].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}}while(1<=o&&0<=i);break}}}finally{U=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?F(e):""}function B(e){switch(e.tag){case 5:return F(e.type);case 16:return F("Lazy");case 13:return F("Suspense");case 19:return F("SuspenseList");case 0:case 2:case 15:return e=A(e.type,!1);case 11:return e=A(e.type.render,!1);case 1:return e=A(e.type,!0);default:return""}}function W(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case S:return"Fragment";case k:return"Portal";case C:return"Profiler";case N:return"StrictMode";case _:return"Suspense";case z:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case j:return(e.displayName||"Context")+".Consumer";case E:return(e._context.displayName||"Context")+".Provider";case P:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case O:return null!==(t=e.displayName||null)?t:W(e.type)||"Memo";case T:t=e._payload,e=e._init;try{return W(e(t))}catch(Ve){}}return null}function $(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return W(t);case 8:return t===N?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function V(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function H(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function Q(e){e._valueTracker||(e._valueTracker=function(e){var t=H(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,l.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function q(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=H(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function G(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function K(e,t){var n=t.checked;return D({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function Y(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=V(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function X(e,t){null!=(t=t.checked)&&b(e,"checked",t,!1)}function J(e,t){X(e,t);var n=V(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,V(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Z(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&G(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+V(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(l(91));return D({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ae(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(l(92));if(te(n)){if(1<n.length)throw Error(l(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:V(n)}}function le(e,t){var n=V(t.value),r=V(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function oe(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function ie(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function se(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?ie(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ue,ce=function(e){return"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(t,n,r,a){MSApp.execUnsafeLocalFunction((function(){return e(t,n)}))}:e}((function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ue=ue||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ue.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}}));function de(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var fe={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},pe=["Webkit","ms","Moz","O"];function me(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||fe.hasOwnProperty(e)&&fe[e]?(""+t).trim():t+"px"}function he(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),a=me(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}Object.keys(fe).forEach((function(e){pe.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),fe[t]=fe[e]}))}));var ge=D({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ve(e,t){if(t){if(ge[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(l(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(l(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(l(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(l(62))}}function ye(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var be=null;function xe(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var we=null,ke=null,Se=null;function Ne(e){if(e=Pa(e)){if("function"!==typeof we)throw Error(l(280));var t=e.stateNode;t&&(t=za(t),we(e.stateNode,e.type,t))}}function Ce(e){ke?Se?Se.push(e):Se=[e]:ke=e}function Ee(){if(ke){var e=ke,t=Se;if(Se=ke=null,Ne(e),t)for(e=0;e<t.length;e++)Ne(t[e])}}function je(e,t){return e(t)}function Pe(){}var _e=!1;function ze(e,t,n){if(_e)return e(t,n);_e=!0;try{return je(e,t,n)}finally{_e=!1,(null!==ke||null!==Se)&&(Pe(),Ee())}}function Oe(e,t){var n=e.stateNode;if(null===n)return null;var r=za(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(l(231,t,typeof n));return n}var Te=!1;if(c)try{var Me={};Object.defineProperty(Me,"passive",{get:function(){Te=!0}}),window.addEventListener("test",Me,Me),window.removeEventListener("test",Me,Me)}catch(Be){Te=!1}function Le(e,t,n,r,a,l,o,i,s){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Re=!1,Ie=null,De=!1,We=null,$e={onError:function(e){Re=!0,Ie=e}};function He(e,t,n,r,a,l,o,i,s){Re=!1,Ie=null,Le.apply($e,arguments)}function Qe(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function qe(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function Ye(e){if(Qe(e)!==e)throw Error(l(188))}function Je(e){return e=function(e){var t=e.alternate;if(!t){if(null===(t=Qe(e)))throw Error(l(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var o=a.alternate;if(null===o){if(null!==(r=a.return)){n=r;continue}break}if(a.child===o.child){for(o=a.child;o;){if(o===n)return Ye(a),e;if(o===r)return Ye(a),t;o=o.sibling}throw Error(l(188))}if(n.return!==r.return)n=a,r=o;else{for(var i=!1,s=a.child;s;){if(s===n){i=!0,n=a,r=o;break}if(s===r){i=!0,r=a,n=o;break}s=s.sibling}if(!i){for(s=o.child;s;){if(s===n){i=!0,n=o,r=a;break}if(s===r){i=!0,r=o,n=a;break}s=s.sibling}if(!i)throw Error(l(189))}}if(n.alternate!==r)throw Error(l(190))}if(3!==n.tag)throw Error(l(188));return n.stateNode.current===n?e:t}(e),null!==e?Ze(e):null}function Ze(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=Ze(e);if(null!==t)return t;e=e.sibling}return null}var et=a.unstable_scheduleCallback,tt=a.unstable_cancelCallback,nt=a.unstable_shouldYield,rt=a.unstable_requestPaint,at=a.unstable_now,lt=a.unstable_getCurrentPriorityLevel,ot=a.unstable_ImmediatePriority,it=a.unstable_UserBlockingPriority,st=a.unstable_NormalPriority,ut=a.unstable_LowPriority,ct=a.unstable_IdlePriority,dt=null,ft=null;var pt=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(mt(e)/ht|0)|0},mt=Math.log,ht=Math.LN2;var vt=64,yt=4194304;function bt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function xt(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,a=e.suspendedLanes,l=e.pingedLanes,o=268435455&n;if(0!==o){var i=o&~a;0!==i?r=bt(i):0!==(l&=o)&&(r=bt(l))}else 0!==(o=n&~a)?r=bt(o):0!==l&&(r=bt(l));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&a)&&((a=r&-r)>=(l=t&-t)||16===a&&0!==(4194240&l)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)a=1<<(n=31-pt(t)),r|=e[n],t&=~a;return r}function wt(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function kt(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function St(){var e=vt;return 0===(4194240&(vt<<=1))&&(vt=64),e}function Nt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ct(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-pt(t)]=n}function Et(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-pt(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var jt=0;function Pt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var _t,zt,Ot,Mt,Lt,Rt=!1,It=[],Dt=null,Ft=null,Ut=null,At=new Map,Bt=new Map,Wt=[],$t="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Vt(e,t){switch(e){case"focusin":case"focusout":Dt=null;break;case"dragenter":case"dragleave":Ft=null;break;case"mouseover":case"mouseout":Ut=null;break;case"pointerover":case"pointerout":At.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Bt.delete(t.pointerId)}}function Ht(e,t,n,r,a,l){return null===e||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:l,targetContainers:[a]},null!==t&&(null!==(t=Pa(t))&&zt(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function Qt(e){var t=ja(e.target);if(null!==t){var n=Qe(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=qe(n)))return e.blockedOn=t,void Lt(e.priority,(function(){Ot(n)}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function qt(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=an(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=Pa(n))&&zt(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);be=r,n.target.dispatchEvent(r),be=null,t.shift()}return!0}function Gt(e,t,n){qt(e)&&n.delete(t)}function Kt(){Rt=!1,null!==Dt&&qt(Dt)&&(Dt=null),null!==Ft&&qt(Ft)&&(Ft=null),null!==Ut&&qt(Ut)&&(Ut=null),At.forEach(Gt),Bt.forEach(Gt)}function Yt(e,t){e.blockedOn===t&&(e.blockedOn=null,Rt||(Rt=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,Kt)))}function Xt(e){function t(t){return Yt(t,e)}if(0<It.length){Yt(It[0],e);for(var n=1;n<It.length;n++){var r=It[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Dt&&Yt(Dt,e),null!==Ft&&Yt(Ft,e),null!==Ut&&Yt(Ut,e),At.forEach(t),Bt.forEach(t),n=0;n<Wt.length;n++)(r=Wt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Wt.length&&null===(n=Wt[0]).blockedOn;)Qt(n),null===n.blockedOn&&Wt.shift()}var Jt=x.ReactCurrentBatchConfig,Zt=!0;function en(e,t,n,r){var a=jt,l=Jt.transition;Jt.transition=null;try{jt=1,nn(e,t,n,r)}finally{jt=a,Jt.transition=l}}function tn(e,t,n,r){var a=jt,l=Jt.transition;Jt.transition=null;try{jt=4,nn(e,t,n,r)}finally{jt=a,Jt.transition=l}}function nn(e,t,n,r){if(Zt){var a=an(e,t,n,r);if(null===a)Zr(e,t,r,rn,n),Vt(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return Dt=Ht(Dt,e,t,n,r,a),!0;case"dragenter":return Ft=Ht(Ft,e,t,n,r,a),!0;case"mouseover":return Ut=Ht(Ut,e,t,n,r,a),!0;case"pointerover":var l=a.pointerId;return At.set(l,Ht(At.get(l)||null,e,t,n,r,a)),!0;case"gotpointercapture":return l=a.pointerId,Bt.set(l,Ht(Bt.get(l)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(Vt(e,r),4&t&&-1<$t.indexOf(e)){for(;null!==a;){var l=Pa(a);if(null!==l&&_t(l),null===(l=an(e,t,n,r))&&Zr(e,t,r,rn,n),l===a)break;a=l}null!==a&&r.stopPropagation()}else Zr(e,t,r,null,n)}}var rn=null;function an(e,t,n,r){if(rn=null,null!==(e=ja(e=xe(r))))if(null===(t=Qe(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=qe(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return rn=e,null}function ln(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(lt()){case ot:return 1;case it:return 4;case st:case ut:return 16;case ct:return 536870912;default:return 16}default:return 16}}var on=null,sn=null,un=null;function cn(){if(un)return un;var e,t,n=sn,r=n.length,a="value"in on?on.value:on.textContent,l=a.length;for(e=0;e<r&&n[e]===a[e];e++);var o=r-e;for(t=1;t<=o&&n[r-t]===a[l-t];t++);return un=a.slice(e,1<t?1-t:void 0)}function dn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function fn(){return!0}function pn(){return!1}function mn(e){function t(t,n,r,a,l){for(var o in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=l,this.currentTarget=null,e)e.hasOwnProperty(o)&&(t=e[o],this[o]=t?t(a):a[o]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?fn:pn,this.isPropagationStopped=pn,this}return D(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=fn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=fn)},persist:function(){},isPersistent:fn}),t}var hn,gn,vn,yn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},bn=mn(yn),xn=D({},yn,{view:0,detail:0}),wn=mn(xn),kn=D({},xn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ln,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==vn&&(vn&&"mousemove"===e.type?(hn=e.screenX-vn.screenX,gn=e.screenY-vn.screenY):gn=hn=0,vn=e),hn)},movementY:function(e){return"movementY"in e?e.movementY:gn}}),Sn=mn(kn),Nn=mn(D({},kn,{dataTransfer:0})),Cn=mn(D({},xn,{relatedTarget:0})),En=mn(D({},yn,{animationName:0,elapsedTime:0,pseudoElement:0})),jn=D({},yn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Pn=mn(jn),_n=mn(D({},yn,{data:0})),zn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},On={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Tn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Mn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Tn[e])&&!!t[e]}function Ln(){return Mn}var Rn=D({},xn,{key:function(e){if(e.key){var t=zn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=dn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?On[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ln,charCode:function(e){return"keypress"===e.type?dn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?dn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),In=mn(Rn),Dn=mn(D({},kn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Fn=mn(D({},xn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ln})),Un=mn(D({},yn,{propertyName:0,elapsedTime:0,pseudoElement:0})),An=D({},kn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Bn=mn(An),Wn=[9,13,27,32],$n=c&&"CompositionEvent"in window,Vn=null;c&&"documentMode"in document&&(Vn=document.documentMode);var Hn=c&&"TextEvent"in window&&!Vn,Qn=c&&(!$n||Vn&&8<Vn&&11>=Vn),qn=String.fromCharCode(32),Gn=!1;function Kn(e,t){switch(e){case"keyup":return-1!==Wn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Yn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Xn=!1;var Jn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Zn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Jn[e.type]:"textarea"===t}function er(e,t,n,r){Ce(r),0<(t=ta(t,"onChange")).length&&(n=new bn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var tr=null,nr=null;function rr(e){qr(e,0)}function ar(e){if(q(_a(e)))return e}function lr(e,t){if("change"===e)return t}var or=!1;if(c){var ir;if(c){var sr="oninput"in document;if(!sr){var ur=document.createElement("div");ur.setAttribute("oninput","return;"),sr="function"===typeof ur.oninput}ir=sr}else ir=!1;or=ir&&(!document.documentMode||9<document.documentMode)}function cr(){tr&&(tr.detachEvent("onpropertychange",dr),nr=tr=null)}function dr(e){if("value"===e.propertyName&&ar(nr)){var t=[];er(t,nr,e,xe(e)),ze(rr,t)}}function fr(e,t,n){"focusin"===e?(cr(),nr=n,(tr=t).attachEvent("onpropertychange",dr)):"focusout"===e&&cr()}function pr(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return ar(nr)}function mr(e,t){if("click"===e)return ar(t)}function hr(e,t){if("input"===e||"change"===e)return ar(t)}var gr="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function vr(e,t){if(gr(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!d.call(t,a)||!gr(e[a],t[a]))return!1}return!0}function yr(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function br(e,t){var n,r=yr(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=yr(r)}}function xr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?xr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function wr(){for(var e=window,t=G();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(Ge){n=!1}if(!n)break;t=G((e=t.contentWindow).document)}return t}function kr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function Sr(e){var t=wr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&xr(n.ownerDocument.documentElement,n)){if(null!==r&&kr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var a=n.textContent.length,l=Math.min(r.start,a);r=void 0===r.end?l:Math.min(r.end,a),!e.extend&&l>r&&(a=r,r=l,l=a),a=br(n,l);var o=br(n,r);a&&o&&(1!==e.rangeCount||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&((t=t.createRange()).setStart(a.node,a.offset),e.removeAllRanges(),l>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Nr=c&&"documentMode"in document&&11>=document.documentMode,Cr=null,Er=null,jr=null,Pr=!1;function _r(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;Pr||null==Cr||Cr!==G(r)||("selectionStart"in(r=Cr)&&kr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},jr&&vr(jr,r)||(jr=r,0<(r=ta(Er,"onSelect")).length&&(t=new bn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Cr)))}function zr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Or={animationend:zr("Animation","AnimationEnd"),animationiteration:zr("Animation","AnimationIteration"),animationstart:zr("Animation","AnimationStart"),transitionend:zr("Transition","TransitionEnd")},Tr={},Mr={};function Lr(e){if(Tr[e])return Tr[e];if(!Or[e])return e;var t,n=Or[e];for(t in n)if(n.hasOwnProperty(t)&&t in Mr)return Tr[e]=n[t];return e}c&&(Mr=document.createElement("div").style,"AnimationEvent"in window||(delete Or.animationend.animation,delete Or.animationiteration.animation,delete Or.animationstart.animation),"TransitionEvent"in window||delete Or.transitionend.transition);var Rr=Lr("animationend"),Ir=Lr("animationiteration"),Dr=Lr("animationstart"),Fr=Lr("transitionend"),Ur=new Map,Ar="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Br(e,t){Ur.set(e,t),s(t,[e])}for(var Wr=0;Wr<Ar.length;Wr++){var $r=Ar[Wr];Br($r.toLowerCase(),"on"+($r[0].toUpperCase()+$r.slice(1)))}Br(Rr,"onAnimationEnd"),Br(Ir,"onAnimationIteration"),Br(Dr,"onAnimationStart"),Br("dblclick","onDoubleClick"),Br("focusin","onFocus"),Br("focusout","onBlur"),Br(Fr,"onTransitionEnd"),u("onMouseEnter",["mouseout","mouseover"]),u("onMouseLeave",["mouseout","mouseover"]),u("onPointerEnter",["pointerout","pointerover"]),u("onPointerLeave",["pointerout","pointerover"]),s("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),s("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),s("onBeforeInput",["compositionend","keypress","textInput","paste"]),s("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Vr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Hr=new Set("cancel close invalid load scroll toggle".split(" ").concat(Vr));function Qr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,a,o,i,s,u){if(He.apply(this,arguments),Re){if(!Re)throw Error(l(198));var c=Ie;Re=!1,Ie=null,De||(De=!0,We=c)}}(r,t,void 0,e),e.currentTarget=null}function qr(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var l=void 0;if(t)for(var o=r.length-1;0<=o;o--){var i=r[o],s=i.instance,u=i.currentTarget;if(i=i.listener,s!==l&&a.isPropagationStopped())break e;Qr(a,i,u),l=s}else for(o=0;o<r.length;o++){if(s=(i=r[o]).instance,u=i.currentTarget,i=i.listener,s!==l&&a.isPropagationStopped())break e;Qr(a,i,u),l=s}}}if(De)throw e=We,De=!1,We=null,e}function Gr(e,t){var n=t[Na];void 0===n&&(n=t[Na]=new Set);var r=e+"__bubble";n.has(r)||(Jr(t,e,2,!1),n.add(r))}function Kr(e,t,n){var r=0;t&&(r|=4),Jr(n,e,r,t)}var Yr="_reactListening"+Math.random().toString(36).slice(2);function Xr(e){if(!e[Yr]){e[Yr]=!0,o.forEach((function(t){"selectionchange"!==t&&(Hr.has(t)||Kr(t,!1,e),Kr(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Yr]||(t[Yr]=!0,Kr("selectionchange",!1,t))}}function Jr(e,t,n,r){switch(ln(t)){case 1:var a=en;break;case 4:a=tn;break;default:a=nn}n=a.bind(null,t,n,e),a=void 0,!Te||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Zr(e,t,n,r,a){var l=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var o=r.tag;if(3===o||4===o){var i=r.stateNode.containerInfo;if(i===a||8===i.nodeType&&i.parentNode===a)break;if(4===o)for(o=r.return;null!==o;){var s=o.tag;if((3===s||4===s)&&((s=o.stateNode.containerInfo)===a||8===s.nodeType&&s.parentNode===a))return;o=o.return}for(;null!==i;){if(null===(o=ja(i)))return;if(5===(s=o.tag)||6===s){r=l=o;continue e}i=i.parentNode}}r=r.return}ze((function(){var r=l,a=xe(n),o=[];e:{var i=Ur.get(e);if(void 0!==i){var s=bn,u=e;switch(e){case"keypress":if(0===dn(n))break e;case"keydown":case"keyup":s=In;break;case"focusin":u="focus",s=Cn;break;case"focusout":u="blur",s=Cn;break;case"beforeblur":case"afterblur":s=Cn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=Sn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=Nn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=Fn;break;case Rr:case Ir:case Dr:s=En;break;case Fr:s=Un;break;case"scroll":s=wn;break;case"wheel":s=Bn;break;case"copy":case"cut":case"paste":s=Pn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=Dn}var c=0!==(4&t),d=!c&&"scroll"===e,f=c?null!==i?i+"Capture":null:i;c=[];for(var p,m=r;null!==m;){var h=(p=m).stateNode;if(5===p.tag&&null!==h&&(p=h,null!==f&&(null!=(h=Oe(m,f))&&c.push(ea(m,h,p)))),d)break;m=m.return}0<c.length&&(i=new s(i,u,null,n,a),o.push({event:i,listeners:c}))}}if(0===(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(i="mouseover"===e||"pointerover"===e)||n===be||!(u=n.relatedTarget||n.fromElement)||!ja(u)&&!u[Sa])&&(s||i)&&(i=a.window===a?a:(i=a.ownerDocument)?i.defaultView||i.parentWindow:window,s?(s=r,null!==(u=(u=n.relatedTarget||n.toElement)?ja(u):null)&&(u!==(d=Qe(u))||5!==u.tag&&6!==u.tag)&&(u=null)):(s=null,u=r),s!==u)){if(c=Sn,h="onMouseLeave",f="onMouseEnter",m="mouse","pointerout"!==e&&"pointerover"!==e||(c=Dn,h="onPointerLeave",f="onPointerEnter",m="pointer"),d=null==s?i:_a(s),p=null==u?i:_a(u),(i=new c(h,m+"leave",s,n,a)).target=d,i.relatedTarget=p,h=null,ja(a)===r&&((c=new c(f,m+"enter",u,n,a)).target=p,c.relatedTarget=d,h=c),d=h,s&&u)e:{for(f=u,m=0,p=c=s;p;p=na(p))m++;for(p=0,h=f;h;h=na(h))p++;for(;0<m-p;)c=na(c),m--;for(;0<p-m;)f=na(f),p--;for(;m--;){if(c===f||null!==f&&c===f.alternate)break e;c=na(c),f=na(f)}c=null}else c=null;null!==s&&ra(o,i,s,c,!1),null!==u&&null!==d&&ra(o,d,u,c,!0)}if("select"===(s=(i=r?_a(r):window).nodeName&&i.nodeName.toLowerCase())||"input"===s&&"file"===i.type)var g=lr;else if(Zn(i))if(or)g=hr;else{g=pr;var v=fr}else(s=i.nodeName)&&"input"===s.toLowerCase()&&("checkbox"===i.type||"radio"===i.type)&&(g=mr);switch(g&&(g=g(e,r))?er(o,g,n,a):(v&&v(e,i,r),"focusout"===e&&(v=i._wrapperState)&&v.controlled&&"number"===i.type&&ee(i,"number",i.value)),v=r?_a(r):window,e){case"focusin":(Zn(v)||"true"===v.contentEditable)&&(Cr=v,Er=r,jr=null);break;case"focusout":jr=Er=Cr=null;break;case"mousedown":Pr=!0;break;case"contextmenu":case"mouseup":case"dragend":Pr=!1,_r(o,n,a);break;case"selectionchange":if(Nr)break;case"keydown":case"keyup":_r(o,n,a)}var y;if($n)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Xn?Kn(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(Qn&&"ko"!==n.locale&&(Xn||"onCompositionStart"!==b?"onCompositionEnd"===b&&Xn&&(y=cn()):(sn="value"in(on=a)?on.value:on.textContent,Xn=!0)),0<(v=ta(r,b)).length&&(b=new _n(b,e,null,n,a),o.push({event:b,listeners:v}),y?b.data=y:null!==(y=Yn(n))&&(b.data=y))),(y=Hn?function(e,t){switch(e){case"compositionend":return Yn(t);case"keypress":return 32!==t.which?null:(Gn=!0,qn);case"textInput":return(e=t.data)===qn&&Gn?null:e;default:return null}}(e,n):function(e,t){if(Xn)return"compositionend"===e||!$n&&Kn(e,t)?(e=cn(),un=sn=on=null,Xn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Qn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=ta(r,"onBeforeInput")).length&&(a=new _n("onBeforeInput","beforeinput",null,n,a),o.push({event:a,listeners:r}),a.data=y))}qr(o,t)}))}function ea(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ta(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,l=a.stateNode;5===a.tag&&null!==l&&(a=l,null!=(l=Oe(e,n))&&r.unshift(ea(e,l,a)),null!=(l=Oe(e,t))&&r.push(ea(e,l,a))),e=e.return}return r}function na(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function ra(e,t,n,r,a){for(var l=t._reactName,o=[];null!==n&&n!==r;){var i=n,s=i.alternate,u=i.stateNode;if(null!==s&&s===r)break;5===i.tag&&null!==u&&(i=u,a?null!=(s=Oe(n,l))&&o.unshift(ea(n,s,i)):a||null!=(s=Oe(n,l))&&o.push(ea(n,s,i))),n=n.return}0!==o.length&&e.push({event:t,listeners:o})}var aa=/\r\n?/g,la=/\u0000|\uFFFD/g;function oa(e){return("string"===typeof e?e:""+e).replace(aa,"\n").replace(la,"")}function ia(e,t,n){if(t=oa(t),oa(e)!==t&&n)throw Error(l(425))}function sa(){}var ua=null,ca=null;function da(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var fa="function"===typeof setTimeout?setTimeout:void 0,pa="function"===typeof clearTimeout?clearTimeout:void 0,ma="function"===typeof Promise?Promise:void 0,ha="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof ma?function(e){return ma.resolve(null).then(e).catch(ga)}:fa;function ga(e){setTimeout((function(){throw e}))}function va(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0===r)return e.removeChild(a),void Xt(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=a}while(n);Xt(t)}function ya(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function ba(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var xa=Math.random().toString(36).slice(2),wa="__reactFiber$"+xa,ka="__reactProps$"+xa,Sa="__reactContainer$"+xa,Na="__reactEvents$"+xa,Ca="__reactListeners$"+xa,Ea="__reactHandles$"+xa;function ja(e){var t=e[wa];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Sa]||n[wa]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=ba(e);null!==e;){if(n=e[wa])return n;e=ba(e)}return t}n=(e=n).parentNode}return null}function Pa(e){return!(e=e[wa]||e[Sa])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function _a(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(l(33))}function za(e){return e[ka]||null}var Oa=[],Ta=-1;function Ma(e){return{current:e}}function La(e){0>Ta||(e.current=Oa[Ta],Oa[Ta]=null,Ta--)}function Ra(e,t){Ta++,Oa[Ta]=e.current,e.current=t}var Ia={},Da=Ma(Ia),Fa=Ma(!1),Ua=Ia;function Aa(e,t){var n=e.type.contextTypes;if(!n)return Ia;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a,l={};for(a in n)l[a]=t[a];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function Ba(e){return null!==(e=e.childContextTypes)&&void 0!==e}function Wa(){La(Fa),La(Da)}function $a(e,t,n){if(Da.current!==Ia)throw Error(l(168));Ra(Da,t),Ra(Fa,n)}function Va(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in t))throw Error(l(108,$(e)||"Unknown",a));return D({},n,r)}function Ha(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Ia,Ua=Da.current,Ra(Da,e),Ra(Fa,Fa.current),!0}function Qa(e,t,n){var r=e.stateNode;if(!r)throw Error(l(169));n?(e=Va(e,t,Ua),r.__reactInternalMemoizedMergedChildContext=e,La(Fa),La(Da),Ra(Da,e)):La(Fa),Ra(Fa,n)}var qa=null,Ga=!1,Ka=!1;function Ya(e){null===qa?qa=[e]:qa.push(e)}function Xa(){if(!Ka&&null!==qa){Ka=!0;var e=0,t=jt;try{var n=qa;for(jt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}qa=null,Ga=!1}catch(Fe){throw null!==qa&&(qa=qa.slice(e+1)),et(ot,Xa),Fe}finally{jt=t,Ka=!1}}return null}var Ja=[],Za=0,el=null,tl=0,nl=[],rl=0,al=null,ll=1,ol="";function il(e,t){Ja[Za++]=tl,Ja[Za++]=el,el=e,tl=t}function sl(e,t,n){nl[rl++]=ll,nl[rl++]=ol,nl[rl++]=al,al=e;var r=ll;e=ol;var a=32-pt(r)-1;r&=~(1<<a),n+=1;var l=32-pt(t)+a;if(30<l){var o=a-a%5;l=(r&(1<<o)-1).toString(32),r>>=o,a-=o,ll=1<<32-pt(t)+a|n<<a|r,ol=l+e}else ll=1<<l|n<<a|r,ol=e}function ul(e){null!==e.return&&(il(e,1),sl(e,1,0))}function cl(e){for(;e===el;)el=Ja[--Za],Ja[Za]=null,tl=Ja[--Za],Ja[Za]=null;for(;e===al;)al=nl[--rl],nl[rl]=null,ol=nl[--rl],nl[rl]=null,ll=nl[--rl],nl[rl]=null}var dl=null,fl=null,pl=!1,ml=null;function hl(e,t){var n=Au(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function gl(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,dl=e,fl=ya(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,dl=e,fl=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==al?{id:ll,overflow:ol}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Au(18,null,null,0)).stateNode=t,n.return=e,e.child=n,dl=e,fl=null,!0);default:return!1}}function vl(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function yl(e){if(pl){var t=fl;if(t){var n=t;if(!gl(e,t)){if(vl(e))throw Error(l(418));t=ya(n.nextSibling);var r=dl;t&&gl(e,t)?hl(r,n):(e.flags=-4097&e.flags|2,pl=!1,dl=e)}}else{if(vl(e))throw Error(l(418));e.flags=-4097&e.flags|2,pl=!1,dl=e}}}function bl(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;dl=e}function xl(e){if(e!==dl)return!1;if(!pl)return bl(e),pl=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!da(e.type,e.memoizedProps)),t&&(t=fl)){if(vl(e))throw wl(),Error(l(418));for(;t;)hl(e,t),t=ya(t.nextSibling)}if(bl(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(l(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){fl=ya(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}fl=null}}else fl=dl?ya(e.stateNode.nextSibling):null;return!0}function wl(){for(var e=fl;e;)e=ya(e.nextSibling)}function kl(){fl=dl=null,pl=!1}function Sl(e){null===ml?ml=[e]:ml.push(e)}var Nl=x.ReactCurrentBatchConfig;function Cl(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(l(309));var r=n.stateNode}if(!r)throw Error(l(147,e));var a=r,o=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===o?t.ref:(t=function(e){var t=a.refs;null===e?delete t[o]:t[o]=e},t._stringRef=o,t)}if("string"!==typeof e)throw Error(l(284));if(!n._owner)throw Error(l(290,e))}return e}function El(e,t){throw e=Object.prototype.toString.call(t),Error(l(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function jl(e){return(0,e._init)(e._payload)}function Pl(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=Wu(e,t)).index=0,e.sibling=null,e}function o(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=2),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Qu(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function u(e,t,n,r){var l=n.type;return l===S?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===l||"object"===typeof l&&null!==l&&l.$$typeof===T&&jl(l)===t.type)?((r=a(t,n.props)).ref=Cl(e,t,n),r.return=e,r):((r=$u(n.type,n.key,n.props,null,e.mode,r)).ref=Cl(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=qu(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,l){return null===t||7!==t.tag?((t=Vu(n,e.mode,r,l)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Qu(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case w:return(n=$u(t.type,t.key,t.props,null,e.mode,n)).ref=Cl(e,null,t),n.return=e,n;case k:return(t=qu(t,e.mode,n)).return=e,t;case T:return f(e,(0,t._init)(t._payload),n)}if(te(t)||R(t))return(t=Vu(t,e.mode,n,null)).return=e,t;El(e,t)}return null}function p(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==a?null:s(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case w:return n.key===a?u(e,t,n,r):null;case k:return n.key===a?c(e,t,n,r):null;case T:return p(e,t,(a=n._init)(n._payload),r)}if(te(n)||R(n))return null!==a?null:d(e,t,n,r,null);El(e,n)}return null}function m(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r)return s(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case w:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case k:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case T:return m(e,t,n,(0,r._init)(r._payload),a)}if(te(r)||R(r))return d(t,e=e.get(n)||null,r,a,null);El(t,r)}return null}function h(a,l,i,s){for(var u=null,c=null,d=l,h=l=0,g=null;null!==d&&h<i.length;h++){d.index>h?(g=d,d=null):g=d.sibling;var v=p(a,d,i[h],s);if(null===v){null===d&&(d=g);break}e&&d&&null===v.alternate&&t(a,d),l=o(v,l,h),null===c?u=v:c.sibling=v,c=v,d=g}if(h===i.length)return n(a,d),pl&&il(a,h),u;if(null===d){for(;h<i.length;h++)null!==(d=f(a,i[h],s))&&(l=o(d,l,h),null===c?u=d:c.sibling=d,c=d);return pl&&il(a,h),u}for(d=r(a,d);h<i.length;h++)null!==(g=m(d,a,h,i[h],s))&&(e&&null!==g.alternate&&d.delete(null===g.key?h:g.key),l=o(g,l,h),null===c?u=g:c.sibling=g,c=g);return e&&d.forEach((function(e){return t(a,e)})),pl&&il(a,h),u}function g(a,i,s,u){var c=R(s);if("function"!==typeof c)throw Error(l(150));if(null==(s=c.call(s)))throw Error(l(151));for(var d=c=null,h=i,g=i=0,v=null,y=s.next();null!==h&&!y.done;g++,y=s.next()){h.index>g?(v=h,h=null):v=h.sibling;var b=p(a,h,y.value,u);if(null===b){null===h&&(h=v);break}e&&h&&null===b.alternate&&t(a,h),i=o(b,i,g),null===d?c=b:d.sibling=b,d=b,h=v}if(y.done)return n(a,h),pl&&il(a,g),c;if(null===h){for(;!y.done;g++,y=s.next())null!==(y=f(a,y.value,u))&&(i=o(y,i,g),null===d?c=y:d.sibling=y,d=y);return pl&&il(a,g),c}for(h=r(a,h);!y.done;g++,y=s.next())null!==(y=m(h,a,g,y.value,u))&&(e&&null!==y.alternate&&h.delete(null===y.key?g:y.key),i=o(y,i,g),null===d?c=y:d.sibling=y,d=y);return e&&h.forEach((function(e){return t(a,e)})),pl&&il(a,g),c}return function e(r,l,o,s){if("object"===typeof o&&null!==o&&o.type===S&&null===o.key&&(o=o.props.children),"object"===typeof o&&null!==o){switch(o.$$typeof){case w:e:{for(var u=o.key,c=l;null!==c;){if(c.key===u){if((u=o.type)===S){if(7===c.tag){n(r,c.sibling),(l=a(c,o.props.children)).return=r,r=l;break e}}else if(c.elementType===u||"object"===typeof u&&null!==u&&u.$$typeof===T&&jl(u)===c.type){n(r,c.sibling),(l=a(c,o.props)).ref=Cl(r,c,o),l.return=r,r=l;break e}n(r,c);break}t(r,c),c=c.sibling}o.type===S?((l=Vu(o.props.children,r.mode,s,o.key)).return=r,r=l):((s=$u(o.type,o.key,o.props,null,r.mode,s)).ref=Cl(r,l,o),s.return=r,r=s)}return i(r);case k:e:{for(c=o.key;null!==l;){if(l.key===c){if(4===l.tag&&l.stateNode.containerInfo===o.containerInfo&&l.stateNode.implementation===o.implementation){n(r,l.sibling),(l=a(l,o.children||[])).return=r,r=l;break e}n(r,l);break}t(r,l),l=l.sibling}(l=qu(o,r.mode,s)).return=r,r=l}return i(r);case T:return e(r,l,(c=o._init)(o._payload),s)}if(te(o))return h(r,l,o,s);if(R(o))return g(r,l,o,s);El(r,o)}return"string"===typeof o&&""!==o||"number"===typeof o?(o=""+o,null!==l&&6===l.tag?(n(r,l.sibling),(l=a(l,o)).return=r,r=l):(n(r,l),(l=Qu(o,r.mode,s)).return=r,r=l),i(r)):n(r,l)}}var _l=Pl(!0),zl=Pl(!1),Ol=Ma(null),Tl=null,Ml=null,Ll=null;function Rl(){Ll=Ml=Tl=null}function Il(e){var t=Ol.current;La(Ol),e._currentValue=t}function Dl(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Fl(e,t){Tl=e,Ll=Ml=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(Pi=!0),e.firstContext=null)}function Ul(e){var t=e._currentValue;if(Ll!==e)if(e={context:e,memoizedValue:t,next:null},null===Ml){if(null===Tl)throw Error(l(308));Ml=e,Tl.dependencies={lanes:0,firstContext:e}}else Ml=Ml.next=e;return t}var Al=null;function Bl(e){null===Al?Al=[e]:Al.push(e)}function Wl(e,t,n,r){var a=t.interleaved;return null===a?(n.next=n,Bl(t)):(n.next=a.next,a.next=n),t.interleaved=n,$l(e,r)}function $l(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Vl=!1;function Hl(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Ql(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function ql(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Gl(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&Ds)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,$l(e,n)}return null===(a=r.interleaved)?(t.next=t,Bl(r)):(t.next=a.next,a.next=t),r.interleaved=t,$l(e,n)}function Kl(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Et(e,n)}}function Yl(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,l=null;if(null!==(n=n.firstBaseUpdate)){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===l?a=l=o:l=l.next=o,n=n.next}while(null!==n);null===l?a=l=t:l=l.next=t}else a=l=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:l,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Xl(e,t,n,r){var a=e.updateQueue;Vl=!1;var l=a.firstBaseUpdate,o=a.lastBaseUpdate,i=a.shared.pending;if(null!==i){a.shared.pending=null;var s=i,u=s.next;s.next=null,null===o?l=u:o.next=u,o=s;var c=e.alternate;null!==c&&((i=(c=c.updateQueue).lastBaseUpdate)!==o&&(null===i?c.firstBaseUpdate=u:i.next=u,c.lastBaseUpdate=s))}if(null!==l){var d=a.baseState;for(o=0,c=u=s=null,i=l;;){var f=i.lane,p=i.eventTime;if((r&f)===f){null!==c&&(c=c.next={eventTime:p,lane:0,tag:i.tag,payload:i.payload,callback:i.callback,next:null});e:{var m=e,h=i;switch(f=t,p=n,h.tag){case 1:if("function"===typeof(m=h.payload)){d=m.call(p,d,f);break e}d=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null===(f="function"===typeof(m=h.payload)?m.call(p,d,f):m)||void 0===f)break e;d=D({},d,f);break e;case 2:Vl=!0}}null!==i.callback&&0!==i.lane&&(e.flags|=64,null===(f=a.effects)?a.effects=[i]:f.push(i))}else p={eventTime:p,lane:f,tag:i.tag,payload:i.payload,callback:i.callback,next:null},null===c?(u=c=p,s=d):c=c.next=p,o|=f;if(null===(i=i.next)){if(null===(i=a.shared.pending))break;i=(f=i).next,f.next=null,a.lastBaseUpdate=f,a.shared.pending=null}}if(null===c&&(s=d),a.baseState=s,a.firstBaseUpdate=u,a.lastBaseUpdate=c,null!==(t=a.shared.interleaved)){a=t;do{o|=a.lane,a=a.next}while(a!==t)}else null===l&&(a.shared.lanes=0);Hs|=o,e.lanes=o,e.memoizedState=d}}function Jl(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(null!==a){if(r.callback=null,r=n,"function"!==typeof a)throw Error(l(191,a));a.call(r)}}}var Zl={},eo=Ma(Zl),to=Ma(Zl),no=Ma(Zl);function ro(e){if(e===Zl)throw Error(l(174));return e}function ao(e,t){switch(Ra(no,t),Ra(to,e),Ra(eo,Zl),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:se(null,"");break;default:t=se(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}La(eo),Ra(eo,t)}function lo(){La(eo),La(to),La(no)}function oo(e){ro(no.current);var t=ro(eo.current),n=se(t,e.type);t!==n&&(Ra(to,e),Ra(eo,n))}function io(e){to.current===e&&(La(eo),La(to))}var so=Ma(0);function uo(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var co=[];function fo(){for(var e=0;e<co.length;e++)co[e]._workInProgressVersionPrimary=null;co.length=0}var po=x.ReactCurrentDispatcher,mo=x.ReactCurrentBatchConfig,ho=0,go=null,vo=null,yo=null,bo=!1,xo=!1,wo=0,ko=0;function So(){throw Error(l(321))}function No(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!gr(e[n],t[n]))return!1;return!0}function Co(e,t,n,r,a,o){if(ho=o,go=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,po.current=null===e||null===e.memoizedState?si:ui,e=n(r,a),xo){o=0;do{if(xo=!1,wo=0,25<=o)throw Error(l(301));o+=1,yo=vo=null,t.updateQueue=null,po.current=ci,e=n(r,a)}while(xo)}if(po.current=ii,t=null!==vo&&null!==vo.next,ho=0,yo=vo=go=null,bo=!1,t)throw Error(l(300));return e}function Eo(){var e=0!==wo;return wo=0,e}function jo(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===yo?go.memoizedState=yo=e:yo=yo.next=e,yo}function Po(){if(null===vo){var e=go.alternate;e=null!==e?e.memoizedState:null}else e=vo.next;var t=null===yo?go.memoizedState:yo.next;if(null!==t)yo=t,vo=e;else{if(null===e)throw Error(l(310));e={memoizedState:(vo=e).memoizedState,baseState:vo.baseState,baseQueue:vo.baseQueue,queue:vo.queue,next:null},null===yo?go.memoizedState=yo=e:yo=yo.next=e}return yo}function _o(e,t){return"function"===typeof t?t(e):t}function zo(e){var t=Po(),n=t.queue;if(null===n)throw Error(l(311));n.lastRenderedReducer=e;var r=vo,a=r.baseQueue,o=n.pending;if(null!==o){if(null!==a){var i=a.next;a.next=o.next,o.next=i}r.baseQueue=a=o,n.pending=null}if(null!==a){o=a.next,r=r.baseState;var s=i=null,u=null,c=o;do{var d=c.lane;if((ho&d)===d)null!==u&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===u?(s=u=f,i=r):u=u.next=f,go.lanes|=d,Hs|=d}c=c.next}while(null!==c&&c!==o);null===u?i=r:u.next=s,gr(r,t.memoizedState)||(Pi=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=u,n.lastRenderedState=r}if(null!==(e=n.interleaved)){a=e;do{o=a.lane,go.lanes|=o,Hs|=o,a=a.next}while(a!==e)}else null===a&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Oo(e){var t=Po(),n=t.queue;if(null===n)throw Error(l(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,o=t.memoizedState;if(null!==a){n.pending=null;var i=a=a.next;do{o=e(o,i.action),i=i.next}while(i!==a);gr(o,t.memoizedState)||(Pi=!0),t.memoizedState=o,null===t.baseQueue&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function To(){}function Mo(e,t){var n=go,r=Po(),a=t(),o=!gr(r.memoizedState,a);if(o&&(r.memoizedState=a,Pi=!0),r=r.queue,Ho(Io.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||null!==yo&&1&yo.memoizedState.tag){if(n.flags|=2048,Ao(9,Ro.bind(null,n,r,a,t),void 0,null),null===Fs)throw Error(l(349));0!==(30&ho)||Lo(n,t,a)}return a}function Lo(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=go.updateQueue)?(t={lastEffect:null,stores:null},go.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Ro(e,t,n,r){t.value=n,t.getSnapshot=r,Do(t)&&Fo(e)}function Io(e,t,n){return n((function(){Do(t)&&Fo(e)}))}function Do(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!gr(e,n)}catch(Ge){return!0}}function Fo(e){var t=$l(e,1);null!==t&&du(t,e,1,-1)}function Uo(e){var t=jo();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:_o,lastRenderedState:e},t.queue=e,e=e.dispatch=ri.bind(null,go,e),[t.memoizedState,e]}function Ao(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=go.updateQueue)?(t={lastEffect:null,stores:null},go.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Bo(){return Po().memoizedState}function Wo(e,t,n,r){var a=jo();go.flags|=e,a.memoizedState=Ao(1|t,n,void 0,void 0===r?null:r)}function $o(e,t,n,r){var a=Po();r=void 0===r?null:r;var l=void 0;if(null!==vo){var o=vo.memoizedState;if(l=o.destroy,null!==r&&No(r,o.deps))return void(a.memoizedState=Ao(t,n,l,r))}go.flags|=e,a.memoizedState=Ao(1|t,n,l,r)}function Vo(e,t){return Wo(8390656,8,e,t)}function Ho(e,t){return $o(2048,8,e,t)}function Qo(e,t){return $o(4,2,e,t)}function qo(e,t){return $o(4,4,e,t)}function Go(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Ko(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,$o(4,4,Go.bind(null,t,e),n)}function Yo(){}function Xo(e,t){var n=Po();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&No(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Jo(e,t){var n=Po();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&No(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Zo(e,t,n){return 0===(21&ho)?(e.baseState&&(e.baseState=!1,Pi=!0),e.memoizedState=n):(gr(n,t)||(n=St(),go.lanes|=n,Hs|=n,e.baseState=!0),t)}function ei(e,t){var n=jt;jt=0!==n&&4>n?n:4,e(!0);var r=mo.transition;mo.transition={};try{e(!1),t()}finally{jt=n,mo.transition=r}}function ti(){return Po().memoizedState}function ni(e,t,n){var r=cu(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},ai(e))li(t,n);else if(null!==(n=Wl(e,t,n,r))){du(n,e,r,uu()),oi(n,t,r)}}function ri(e,t,n){var r=cu(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(ai(e))li(t,a);else{var l=e.alternate;if(0===e.lanes&&(null===l||0===l.lanes)&&null!==(l=t.lastRenderedReducer))try{var o=t.lastRenderedState,i=l(o,n);if(a.hasEagerState=!0,a.eagerState=i,gr(i,o)){var s=t.interleaved;return null===s?(a.next=a,Bl(t)):(a.next=s.next,s.next=a),void(t.interleaved=a)}}catch(Ae){}null!==(n=Wl(e,t,a,r))&&(du(n,e,r,a=uu()),oi(n,t,r))}}function ai(e){var t=e.alternate;return e===go||null!==t&&t===go}function li(e,t){xo=bo=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function oi(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Et(e,n)}}var ii={readContext:Ul,useCallback:So,useContext:So,useEffect:So,useImperativeHandle:So,useInsertionEffect:So,useLayoutEffect:So,useMemo:So,useReducer:So,useRef:So,useState:So,useDebugValue:So,useDeferredValue:So,useTransition:So,useMutableSource:So,useSyncExternalStore:So,useId:So,unstable_isNewReconciler:!1},si={readContext:Ul,useCallback:function(e,t){return jo().memoizedState=[e,void 0===t?null:t],e},useContext:Ul,useEffect:Vo,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Wo(4194308,4,Go.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Wo(4194308,4,e,t)},useInsertionEffect:function(e,t){return Wo(4,2,e,t)},useMemo:function(e,t){var n=jo();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=jo();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=ni.bind(null,go,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},jo().memoizedState=e},useState:Uo,useDebugValue:Yo,useDeferredValue:function(e){return jo().memoizedState=e},useTransition:function(){var e=Uo(!1),t=e[0];return e=ei.bind(null,e[1]),jo().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=go,a=jo();if(pl){if(void 0===n)throw Error(l(407));n=n()}else{if(n=t(),null===Fs)throw Error(l(349));0!==(30&ho)||Lo(r,t,n)}a.memoizedState=n;var o={value:n,getSnapshot:t};return a.queue=o,Vo(Io.bind(null,r,o,e),[e]),r.flags|=2048,Ao(9,Ro.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=jo(),t=Fs.identifierPrefix;if(pl){var n=ol;t=":"+t+"R"+(n=(ll&~(1<<32-pt(ll)-1)).toString(32)+n),0<(n=wo++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=ko++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},ui={readContext:Ul,useCallback:Xo,useContext:Ul,useEffect:Ho,useImperativeHandle:Ko,useInsertionEffect:Qo,useLayoutEffect:qo,useMemo:Jo,useReducer:zo,useRef:Bo,useState:function(){return zo(_o)},useDebugValue:Yo,useDeferredValue:function(e){return Zo(Po(),vo.memoizedState,e)},useTransition:function(){return[zo(_o)[0],Po().memoizedState]},useMutableSource:To,useSyncExternalStore:Mo,useId:ti,unstable_isNewReconciler:!1},ci={readContext:Ul,useCallback:Xo,useContext:Ul,useEffect:Ho,useImperativeHandle:Ko,useInsertionEffect:Qo,useLayoutEffect:qo,useMemo:Jo,useReducer:Oo,useRef:Bo,useState:function(){return Oo(_o)},useDebugValue:Yo,useDeferredValue:function(e){var t=Po();return null===vo?t.memoizedState=e:Zo(t,vo.memoizedState,e)},useTransition:function(){return[Oo(_o)[0],Po().memoizedState]},useMutableSource:To,useSyncExternalStore:Mo,useId:ti,unstable_isNewReconciler:!1};function di(e,t){if(e&&e.defaultProps){for(var n in t=D({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function fi(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:D({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var pi={isMounted:function(e){return!!(e=e._reactInternals)&&Qe(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=uu(),a=cu(e),l=ql(r,a);l.payload=t,void 0!==n&&null!==n&&(l.callback=n),null!==(t=Gl(e,l,a))&&(du(t,e,a,r),Kl(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=uu(),a=cu(e),l=ql(r,a);l.tag=1,l.payload=t,void 0!==n&&null!==n&&(l.callback=n),null!==(t=Gl(e,l,a))&&(du(t,e,a,r),Kl(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=uu(),r=cu(e),a=ql(n,r);a.tag=2,void 0!==t&&null!==t&&(a.callback=t),null!==(t=Gl(e,a,r))&&(du(t,e,r,n),Kl(t,e,r))}};function mi(e,t,n,r,a,l,o){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,l,o):!t.prototype||!t.prototype.isPureReactComponent||(!vr(n,r)||!vr(a,l))}function hi(e,t,n){var r=!1,a=Ia,l=t.contextType;return"object"===typeof l&&null!==l?l=Ul(l):(a=Ba(t)?Ua:Da.current,l=(r=null!==(r=t.contextTypes)&&void 0!==r)?Aa(e,a):Ia),t=new t(n,l),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=pi,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=l),t}function gi(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&pi.enqueueReplaceState(t,t.state,null)}function vi(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},Hl(e);var l=t.contextType;"object"===typeof l&&null!==l?a.context=Ul(l):(l=Ba(t)?Ua:Da.current,a.context=Aa(e,l)),a.state=e.memoizedState,"function"===typeof(l=t.getDerivedStateFromProps)&&(fi(e,t,l,n),a.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof a.getSnapshotBeforeUpdate||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||(t=a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&pi.enqueueReplaceState(a,a.state,null),Xl(e,n,a,r),a.state=e.memoizedState),"function"===typeof a.componentDidMount&&(e.flags|=4194308)}function yi(e,t){try{var n="",r=t;do{n+=B(r),r=r.return}while(r);var a=n}catch(Ke){a="\nError generating stack: "+Ke.message+"\n"+Ke.stack}return{value:e,source:t,stack:a,digest:null}}function bi(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function xi(e,t){try{console.error(t.value)}catch(Ve){setTimeout((function(){throw Ve}))}}var wi="function"===typeof WeakMap?WeakMap:Map;function ki(e,t,n){(n=ql(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Zs||(Zs=!0,eu=r),xi(0,t)},n}function Si(e,t,n){(n=ql(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){xi(0,t)}}var l=e.stateNode;return null!==l&&"function"===typeof l.componentDidCatch&&(n.callback=function(){xi(0,t),"function"!==typeof r&&(null===tu?tu=new Set([this]):tu.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function Ni(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new wi;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=Lu.bind(null,e,t,n),t.then(e,e))}function Ci(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function Ei(e,t,n,r,a){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=ql(-1,1)).tag=2,Gl(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=a,e)}var ji=x.ReactCurrentOwner,Pi=!1;function _i(e,t,n,r){t.child=null===e?zl(t,null,n,r):_l(t,e.child,n,r)}function zi(e,t,n,r,a){n=n.render;var l=t.ref;return Fl(t,a),r=Co(e,t,n,r,l,a),n=Eo(),null===e||Pi?(pl&&n&&ul(t),t.flags|=1,_i(e,t,r,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Zi(e,t,a))}function Oi(e,t,n,r,a){if(null===e){var l=n.type;return"function"!==typeof l||Bu(l)||void 0!==l.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=$u(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=l,Ti(e,t,l,r,a))}if(l=e.child,0===(e.lanes&a)){var o=l.memoizedProps;if((n=null!==(n=n.compare)?n:vr)(o,r)&&e.ref===t.ref)return Zi(e,t,a)}return t.flags|=1,(e=Wu(l,r)).ref=t.ref,e.return=t,t.child=e}function Ti(e,t,n,r,a){if(null!==e){var l=e.memoizedProps;if(vr(l,r)&&e.ref===t.ref){if(Pi=!1,t.pendingProps=r=l,0===(e.lanes&a))return t.lanes=e.lanes,Zi(e,t,a);0!==(131072&e.flags)&&(Pi=!0)}}return Ri(e,t,n,r,a)}function Mi(e,t,n){var r=t.pendingProps,a=r.children,l=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ra(Ws,Bs),Bs|=n;else{if(0===(1073741824&n))return e=null!==l?l.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Ra(Ws,Bs),Bs|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==l?l.baseLanes:n,Ra(Ws,Bs),Bs|=r}else null!==l?(r=l.baseLanes|n,t.memoizedState=null):r=n,Ra(Ws,Bs),Bs|=r;return _i(e,t,a,n),t.child}function Li(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Ri(e,t,n,r,a){var l=Ba(n)?Ua:Da.current;return l=Aa(t,l),Fl(t,a),n=Co(e,t,n,r,l,a),r=Eo(),null===e||Pi?(pl&&r&&ul(t),t.flags|=1,_i(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Zi(e,t,a))}function Ii(e,t,n,r,a){if(Ba(n)){var l=!0;Ha(t)}else l=!1;if(Fl(t,a),null===t.stateNode)Ji(e,t),hi(t,n,r),vi(t,n,r,a),r=!0;else if(null===e){var o=t.stateNode,i=t.memoizedProps;o.props=i;var s=o.context,u=n.contextType;"object"===typeof u&&null!==u?u=Ul(u):u=Aa(t,u=Ba(n)?Ua:Da.current);var c=n.getDerivedStateFromProps,d="function"===typeof c||"function"===typeof o.getSnapshotBeforeUpdate;d||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(i!==r||s!==u)&&gi(t,o,r,u),Vl=!1;var f=t.memoizedState;o.state=f,Xl(t,r,o,a),s=t.memoizedState,i!==r||f!==s||Fa.current||Vl?("function"===typeof c&&(fi(t,n,c,r),s=t.memoizedState),(i=Vl||mi(t,n,i,r,f,s,u))?(d||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||("function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount()),"function"===typeof o.componentDidMount&&(t.flags|=4194308)):("function"===typeof o.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),o.props=r,o.state=s,o.context=u,r=i):("function"===typeof o.componentDidMount&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,Ql(e,t),i=t.memoizedProps,u=t.type===t.elementType?i:di(t.type,i),o.props=u,d=t.pendingProps,f=o.context,"object"===typeof(s=n.contextType)&&null!==s?s=Ul(s):s=Aa(t,s=Ba(n)?Ua:Da.current);var p=n.getDerivedStateFromProps;(c="function"===typeof p||"function"===typeof o.getSnapshotBeforeUpdate)||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(i!==d||f!==s)&&gi(t,o,r,s),Vl=!1,f=t.memoizedState,o.state=f,Xl(t,r,o,a);var m=t.memoizedState;i!==d||f!==m||Fa.current||Vl?("function"===typeof p&&(fi(t,n,p,r),m=t.memoizedState),(u=Vl||mi(t,n,u,r,f,m,s)||!1)?(c||"function"!==typeof o.UNSAFE_componentWillUpdate&&"function"!==typeof o.componentWillUpdate||("function"===typeof o.componentWillUpdate&&o.componentWillUpdate(r,m,s),"function"===typeof o.UNSAFE_componentWillUpdate&&o.UNSAFE_componentWillUpdate(r,m,s)),"function"===typeof o.componentDidUpdate&&(t.flags|=4),"function"===typeof o.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof o.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=m),o.props=r,o.state=m,o.context=s,r=u):("function"!==typeof o.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Di(e,t,n,r,l,a)}function Di(e,t,n,r,a,l){Li(e,t);var o=0!==(128&t.flags);if(!r&&!o)return a&&Qa(t,n,!1),Zi(e,t,l);r=t.stateNode,ji.current=t;var i=o&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&o?(t.child=_l(t,e.child,null,l),t.child=_l(t,null,i,l)):_i(e,t,i,l),t.memoizedState=r.state,a&&Qa(t,n,!0),t.child}function Fi(e){var t=e.stateNode;t.pendingContext?$a(0,t.pendingContext,t.pendingContext!==t.context):t.context&&$a(0,t.context,!1),ao(e,t.containerInfo)}function Ui(e,t,n,r,a){return kl(),Sl(a),t.flags|=256,_i(e,t,n,r),t.child}var Ai,Bi,Wi,$i,Vi={dehydrated:null,treeContext:null,retryLane:0};function Hi(e){return{baseLanes:e,cachePool:null,transitions:null}}function Qi(e,t,n){var r,a=t.pendingProps,o=so.current,i=!1,s=0!==(128&t.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&0!==(2&o)),r?(i=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(o|=1),Ra(so,1&o),null===e)return yl(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(s=a.children,e=a.fallback,i?(a=t.mode,i=t.child,s={mode:"hidden",children:s},0===(1&a)&&null!==i?(i.childLanes=0,i.pendingProps=s):i=Hu(s,a,0,null),e=Vu(e,a,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Hi(n),t.memoizedState=Vi,e):qi(t,s));if(null!==(o=e.memoizedState)&&null!==(r=o.dehydrated))return function(e,t,n,r,a,o,i){if(n)return 256&t.flags?(t.flags&=-257,Gi(e,t,i,r=bi(Error(l(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(o=r.fallback,a=t.mode,r=Hu({mode:"visible",children:r.children},a,0,null),(o=Vu(o,a,i,null)).flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,0!==(1&t.mode)&&_l(t,e.child,null,i),t.child.memoizedState=Hi(i),t.memoizedState=Vi,o);if(0===(1&t.mode))return Gi(e,t,i,null);if("$!"===a.data){if(r=a.nextSibling&&a.nextSibling.dataset)var s=r.dgst;return r=s,Gi(e,t,i,r=bi(o=Error(l(419)),r,void 0))}if(s=0!==(i&e.childLanes),Pi||s){if(null!==(r=Fs)){switch(i&-i){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}0!==(a=0!==(a&(r.suspendedLanes|i))?0:a)&&a!==o.retryLane&&(o.retryLane=a,$l(e,a),du(r,e,a,-1))}return Nu(),Gi(e,t,i,r=bi(Error(l(421))))}return"$?"===a.data?(t.flags|=128,t.child=e.child,t=Iu.bind(null,e),a._reactRetry=t,null):(e=o.treeContext,fl=ya(a.nextSibling),dl=t,pl=!0,ml=null,null!==e&&(nl[rl++]=ll,nl[rl++]=ol,nl[rl++]=al,ll=e.id,ol=e.overflow,al=t),t=qi(t,r.children),t.flags|=4096,t)}(e,t,s,a,r,o,n);if(i){i=a.fallback,s=t.mode,r=(o=e.child).sibling;var u={mode:"hidden",children:a.children};return 0===(1&s)&&t.child!==o?((a=t.child).childLanes=0,a.pendingProps=u,t.deletions=null):(a=Wu(o,u)).subtreeFlags=14680064&o.subtreeFlags,null!==r?i=Wu(r,i):(i=Vu(i,s,n,null)).flags|=2,i.return=t,a.return=t,a.sibling=i,t.child=a,a=i,i=t.child,s=null===(s=e.child.memoizedState)?Hi(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=Vi,a}return e=(i=e.child).sibling,a=Wu(i,{mode:"visible",children:a.children}),0===(1&t.mode)&&(a.lanes=n),a.return=t,a.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=a,t.memoizedState=null,a}function qi(e,t){return(t=Hu({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Gi(e,t,n,r){return null!==r&&Sl(r),_l(t,e.child,null,n),(e=qi(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Ki(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Dl(e.return,t,n)}function Yi(e,t,n,r,a){var l=e.memoizedState;null===l?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=n,l.tailMode=a)}function Xi(e,t,n){var r=t.pendingProps,a=r.revealOrder,l=r.tail;if(_i(e,t,r.children,n),0!==(2&(r=so.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Ki(e,n,t);else if(19===e.tag)Ki(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Ra(so,r),0===(1&t.mode))t.memoizedState=null;else switch(a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===uo(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Yi(t,!1,a,n,l);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===uo(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Yi(t,!0,n,null,l);break;case"together":Yi(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ji(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Zi(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Hs|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(l(153));if(null!==t.child){for(n=Wu(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Wu(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function es(e,t){if(!pl)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ts(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=14680064&a.subtreeFlags,r|=14680064&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function ns(e,t,n){var r=t.pendingProps;switch(cl(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ts(t),null;case 1:case 17:return Ba(t.type)&&Wa(),ts(t),null;case 3:return r=t.stateNode,lo(),La(Fa),La(Da),fo(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(xl(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==ml&&(hu(ml),ml=null))),Bi(e,t),ts(t),null;case 5:io(t);var a=ro(no.current);if(n=t.type,null!==e&&null!=t.stateNode)Wi(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(l(166));return ts(t),null}if(e=ro(eo.current),xl(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[wa]=t,r[ka]=o,e=0!==(1&t.mode),n){case"dialog":Gr("cancel",r),Gr("close",r);break;case"iframe":case"object":case"embed":Gr("load",r);break;case"video":case"audio":for(a=0;a<Vr.length;a++)Gr(Vr[a],r);break;case"source":Gr("error",r);break;case"img":case"image":case"link":Gr("error",r),Gr("load",r);break;case"details":Gr("toggle",r);break;case"input":Y(r,o),Gr("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},Gr("invalid",r);break;case"textarea":ae(r,o),Gr("invalid",r)}for(var s in ve(n,o),a=null,o)if(o.hasOwnProperty(s)){var u=o[s];"children"===s?"string"===typeof u?r.textContent!==u&&(!0!==o.suppressHydrationWarning&&ia(r.textContent,u,e),a=["children",u]):"number"===typeof u&&r.textContent!==""+u&&(!0!==o.suppressHydrationWarning&&ia(r.textContent,u,e),a=["children",""+u]):i.hasOwnProperty(s)&&null!=u&&"onScroll"===s&&Gr("scroll",r)}switch(n){case"input":Q(r),Z(r,o,!0);break;case"textarea":Q(r),oe(r);break;case"select":case"option":break;default:"function"===typeof o.onClick&&(r.onclick=sa)}r=a,t.updateQueue=r,null!==r&&(t.flags|=4)}else{s=9===a.nodeType?a:a.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=ie(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=s.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),"select"===n&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[wa]=t,e[ka]=r,Ai(e,t,!1,!1),t.stateNode=e;e:{switch(s=ye(n,r),n){case"dialog":Gr("cancel",e),Gr("close",e),a=r;break;case"iframe":case"object":case"embed":Gr("load",e),a=r;break;case"video":case"audio":for(a=0;a<Vr.length;a++)Gr(Vr[a],e);a=r;break;case"source":Gr("error",e),a=r;break;case"img":case"image":case"link":Gr("error",e),Gr("load",e),a=r;break;case"details":Gr("toggle",e),a=r;break;case"input":Y(e,r),a=K(e,r),Gr("invalid",e);break;case"option":default:a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=D({},r,{value:void 0}),Gr("invalid",e);break;case"textarea":ae(e,r),a=re(e,r),Gr("invalid",e)}for(o in ve(n,a),u=a)if(u.hasOwnProperty(o)){var c=u[o];"style"===o?he(e,c):"dangerouslySetInnerHTML"===o?null!=(c=c?c.__html:void 0)&&ce(e,c):"children"===o?"string"===typeof c?("textarea"!==n||""!==c)&&de(e,c):"number"===typeof c&&de(e,""+c):"suppressContentEditableWarning"!==o&&"suppressHydrationWarning"!==o&&"autoFocus"!==o&&(i.hasOwnProperty(o)?null!=c&&"onScroll"===o&&Gr("scroll",e):null!=c&&b(e,o,c,s))}switch(n){case"input":Q(e),Z(e,r,!1);break;case"textarea":Q(e),oe(e);break;case"option":null!=r.value&&e.setAttribute("value",""+V(r.value));break;case"select":e.multiple=!!r.multiple,null!=(o=r.value)?ne(e,!!r.multiple,o,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof a.onClick&&(e.onclick=sa)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return ts(t),null;case 6:if(e&&null!=t.stateNode)$i(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(l(166));if(n=ro(no.current),ro(eo.current),xl(t)){if(r=t.stateNode,n=t.memoizedProps,r[wa]=t,(o=r.nodeValue!==n)&&null!==(e=dl))switch(e.tag){case 3:ia(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&ia(r.nodeValue,n,0!==(1&e.mode))}o&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[wa]=t,t.stateNode=r}return ts(t),null;case 13:if(La(so),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(pl&&null!==fl&&0!==(1&t.mode)&&0===(128&t.flags))wl(),kl(),t.flags|=98560,o=!1;else if(o=xl(t),null!==r&&null!==r.dehydrated){if(null===e){if(!o)throw Error(l(318));if(!(o=null!==(o=t.memoizedState)?o.dehydrated:null))throw Error(l(317));o[wa]=t}else kl(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;ts(t),o=!1}else null!==ml&&(hu(ml),ml=null),o=!0;if(!o)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&so.current)?0===$s&&($s=3):Nu())),null!==t.updateQueue&&(t.flags|=4),ts(t),null);case 4:return lo(),Bi(e,t),null===e&&Xr(t.stateNode.containerInfo),ts(t),null;case 10:return Il(t.type._context),ts(t),null;case 19:if(La(so),null===(o=t.memoizedState))return ts(t),null;if(r=0!==(128&t.flags),null===(s=o.rendering))if(r)es(o,!1);else{if(0!==$s||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(s=uo(e))){for(t.flags|=128,es(o,!1),null!==(r=s.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(o=n).flags&=14680066,null===(s=o.alternate)?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Ra(so,1&so.current|2),t.child}e=e.sibling}null!==o.tail&&at()>Xs&&(t.flags|=128,r=!0,es(o,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=uo(s))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),es(o,!0),null===o.tail&&"hidden"===o.tailMode&&!s.alternate&&!pl)return ts(t),null}else 2*at()-o.renderingStartTime>Xs&&1073741824!==n&&(t.flags|=128,r=!0,es(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(null!==(n=o.last)?n.sibling=s:t.child=s,o.last=s)}return null!==o.tail?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=at(),t.sibling=null,n=so.current,Ra(so,r?1&n|2:1&n),t):(ts(t),null);case 22:case 23:return xu(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Bs)&&(ts(t),6&t.subtreeFlags&&(t.flags|=8192)):ts(t),null;case 24:case 25:return null}throw Error(l(156,t.tag))}function rs(e,t){switch(cl(t),t.tag){case 1:return Ba(t.type)&&Wa(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return lo(),La(Fa),La(Da),fo(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return io(t),null;case 13:if(La(so),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(l(340));kl()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return La(so),null;case 4:return lo(),null;case 10:return Il(t.type._context),null;case 22:case 23:return xu(),null;default:return null}}Ai=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Bi=function(){},Wi=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,ro(eo.current);var l,o=null;switch(n){case"input":a=K(e,a),r=K(e,r),o=[];break;case"select":a=D({},a,{value:void 0}),r=D({},r,{value:void 0}),o=[];break;case"textarea":a=re(e,a),r=re(e,r),o=[];break;default:"function"!==typeof a.onClick&&"function"===typeof r.onClick&&(e.onclick=sa)}for(c in ve(n,r),n=null,a)if(!r.hasOwnProperty(c)&&a.hasOwnProperty(c)&&null!=a[c])if("style"===c){var s=a[c];for(l in s)s.hasOwnProperty(l)&&(n||(n={}),n[l]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(i.hasOwnProperty(c)?o||(o=[]):(o=o||[]).push(c,null));for(c in r){var u=r[c];if(s=null!=a?a[c]:void 0,r.hasOwnProperty(c)&&u!==s&&(null!=u||null!=s))if("style"===c)if(s){for(l in s)!s.hasOwnProperty(l)||u&&u.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in u)u.hasOwnProperty(l)&&s[l]!==u[l]&&(n||(n={}),n[l]=u[l])}else n||(o||(o=[]),o.push(c,n)),n=u;else"dangerouslySetInnerHTML"===c?(u=u?u.__html:void 0,s=s?s.__html:void 0,null!=u&&s!==u&&(o=o||[]).push(c,u)):"children"===c?"string"!==typeof u&&"number"!==typeof u||(o=o||[]).push(c,""+u):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(i.hasOwnProperty(c)?(null!=u&&"onScroll"===c&&Gr("scroll",e),o||s===u||(o=[])):(o=o||[]).push(c,u))}n&&(o=o||[]).push("style",n);var c=o;(t.updateQueue=c)&&(t.flags|=4)}},$i=function(e,t,n,r){n!==r&&(t.flags|=4)};var as=!1,ls=!1,os="function"===typeof WeakSet?WeakSet:Set,is=null;function ss(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(Ge){Mu(e,t,Ge)}else n.current=null}function us(e,t,n){try{n()}catch(Ge){Mu(e,t,Ge)}}var cs=!1;function ds(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var a=r=r.next;do{if((a.tag&e)===e){var l=a.destroy;a.destroy=void 0,void 0!==l&&us(t,n,l)}a=a.next}while(a!==r)}}function fs(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ps(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function ms(e){var t=e.alternate;null!==t&&(e.alternate=null,ms(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[wa],delete t[ka],delete t[Na],delete t[Ca],delete t[Ea])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function hs(e){return 5===e.tag||3===e.tag||4===e.tag}function gs(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||hs(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function vs(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=sa));else if(4!==r&&null!==(e=e.child))for(vs(e,t,n),e=e.sibling;null!==e;)vs(e,t,n),e=e.sibling}function ys(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(ys(e,t,n),e=e.sibling;null!==e;)ys(e,t,n),e=e.sibling}var bs=null,xs=!1;function ws(e,t,n){for(n=n.child;null!==n;)ks(e,t,n),n=n.sibling}function ks(e,t,n){if(ft&&"function"===typeof ft.onCommitFiberUnmount)try{ft.onCommitFiberUnmount(dt,n)}catch(Xe){}switch(n.tag){case 5:ls||ss(n,t);case 6:var r=bs,a=xs;bs=null,ws(e,t,n),xs=a,null!==(bs=r)&&(xs?(e=bs,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):bs.removeChild(n.stateNode));break;case 18:null!==bs&&(xs?(e=bs,n=n.stateNode,8===e.nodeType?va(e.parentNode,n):1===e.nodeType&&va(e,n),Xt(e)):va(bs,n.stateNode));break;case 4:r=bs,a=xs,bs=n.stateNode.containerInfo,xs=!0,ws(e,t,n),bs=r,xs=a;break;case 0:case 11:case 14:case 15:if(!ls&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){a=r=r.next;do{var l=a,o=l.destroy;l=l.tag,void 0!==o&&(0!==(2&l)||0!==(4&l))&&us(n,t,o),a=a.next}while(a!==r)}ws(e,t,n);break;case 1:if(!ls&&(ss(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(Xe){Mu(n,t,Xe)}ws(e,t,n);break;case 21:ws(e,t,n);break;case 22:1&n.mode?(ls=(r=ls)||null!==n.memoizedState,ws(e,t,n),ls=r):ws(e,t,n);break;default:ws(e,t,n)}}function Ss(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new os),t.forEach((function(t){var r=Du.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}function Ns(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r];try{var o=e,i=t,s=i;e:for(;null!==s;){switch(s.tag){case 5:bs=s.stateNode,xs=!1;break e;case 3:case 4:bs=s.stateNode.containerInfo,xs=!0;break e}s=s.return}if(null===bs)throw Error(l(160));ks(o,i,a),bs=null,xs=!1;var u=a.alternate;null!==u&&(u.return=null),a.return=null}catch(Ae){Mu(a,t,Ae)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)Cs(t,e),t=t.sibling}function Cs(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ns(t,e),Es(e),4&r){try{ds(3,e,e.return),fs(3,e)}catch(Ue){Mu(e,e.return,Ue)}try{ds(5,e,e.return)}catch(Ue){Mu(e,e.return,Ue)}}break;case 1:Ns(t,e),Es(e),512&r&&null!==n&&ss(n,n.return);break;case 5:if(Ns(t,e),Es(e),512&r&&null!==n&&ss(n,n.return),32&e.flags){var a=e.stateNode;try{de(a,"")}catch(Ue){Mu(e,e.return,Ue)}}if(4&r&&null!=(a=e.stateNode)){var o=e.memoizedProps,i=null!==n?n.memoizedProps:o,s=e.type,u=e.updateQueue;if(e.updateQueue=null,null!==u)try{"input"===s&&"radio"===o.type&&null!=o.name&&X(a,o),ye(s,i);var c=ye(s,o);for(i=0;i<u.length;i+=2){var d=u[i],f=u[i+1];"style"===d?he(a,f):"dangerouslySetInnerHTML"===d?ce(a,f):"children"===d?de(a,f):b(a,d,f,c)}switch(s){case"input":J(a,o);break;case"textarea":le(a,o);break;case"select":var p=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!o.multiple;var m=o.value;null!=m?ne(a,!!o.multiple,m,!1):p!==!!o.multiple&&(null!=o.defaultValue?ne(a,!!o.multiple,o.defaultValue,!0):ne(a,!!o.multiple,o.multiple?[]:"",!1))}a[ka]=o}catch(Ue){Mu(e,e.return,Ue)}}break;case 6:if(Ns(t,e),Es(e),4&r){if(null===e.stateNode)throw Error(l(162));a=e.stateNode,o=e.memoizedProps;try{a.nodeValue=o}catch(Ue){Mu(e,e.return,Ue)}}break;case 3:if(Ns(t,e),Es(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Xt(t.containerInfo)}catch(Ue){Mu(e,e.return,Ue)}break;case 4:default:Ns(t,e),Es(e);break;case 13:Ns(t,e),Es(e),8192&(a=e.child).flags&&(o=null!==a.memoizedState,a.stateNode.isHidden=o,!o||null!==a.alternate&&null!==a.alternate.memoizedState||(Ys=at())),4&r&&Ss(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(ls=(c=ls)||d,Ns(t,e),ls=c):Ns(t,e),Es(e),8192&r){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!d&&0!==(1&e.mode))for(is=e,d=e.child;null!==d;){for(f=is=d;null!==is;){switch(m=(p=is).child,p.tag){case 0:case 11:case 14:case 15:ds(4,p,p.return);break;case 1:ss(p,p.return);var h=p.stateNode;if("function"===typeof h.componentWillUnmount){r=p,n=p.return;try{t=r,h.props=t.memoizedProps,h.state=t.memoizedState,h.componentWillUnmount()}catch(Ue){Mu(r,n,Ue)}}break;case 5:ss(p,p.return);break;case 22:if(null!==p.memoizedState){zs(f);continue}}null!==m?(m.return=p,is=m):zs(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{a=f.stateNode,c?"function"===typeof(o=a.style).setProperty?o.setProperty("display","none","important"):o.display="none":(s=f.stateNode,i=void 0!==(u=f.memoizedProps.style)&&null!==u&&u.hasOwnProperty("display")?u.display:null,s.style.display=me("display",i))}catch(Ue){Mu(e,e.return,Ue)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(Ue){Mu(e,e.return,Ue)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:Ns(t,e),Es(e),4&r&&Ss(e);case 21:}}function Es(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(hs(n)){var r=n;break e}n=n.return}throw Error(l(160))}switch(r.tag){case 5:var a=r.stateNode;32&r.flags&&(de(a,""),r.flags&=-33),ys(e,gs(e),a);break;case 3:case 4:var o=r.stateNode.containerInfo;vs(e,gs(e),o);break;default:throw Error(l(161))}}catch(Tt){Mu(e,e.return,Tt)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function js(e,t,n){is=e,Ps(e,t,n)}function Ps(e,t,n){for(var r=0!==(1&e.mode);null!==is;){var a=is,l=a.child;if(22===a.tag&&r){var o=null!==a.memoizedState||as;if(!o){var i=a.alternate,s=null!==i&&null!==i.memoizedState||ls;i=as;var u=ls;if(as=o,(ls=s)&&!u)for(is=a;null!==is;)s=(o=is).child,22===o.tag&&null!==o.memoizedState?Os(a):null!==s?(s.return=o,is=s):Os(a);for(;null!==l;)is=l,Ps(l,t,n),l=l.sibling;is=a,as=i,ls=u}_s(e)}else 0!==(8772&a.subtreeFlags)&&null!==l?(l.return=a,is=l):_s(e)}}function _s(e){for(;null!==is;){var t=is;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:ls||fs(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!ls)if(null===n)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:di(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;null!==o&&Jl(t,o,r);break;case 3:var i=t.updateQueue;if(null!==i){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Jl(t,i,n)}break;case 5:var s=t.stateNode;if(null===n&&4&t.flags){n=s;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var d=c.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Xt(f)}}}break;default:throw Error(l(163))}ls||512&t.flags&&ps(t)}catch(p){Mu(t,t.return,p)}}if(t===e){is=null;break}if(null!==(n=t.sibling)){n.return=t.return,is=n;break}is=t.return}}function zs(e){for(;null!==is;){var t=is;if(t===e){is=null;break}var n=t.sibling;if(null!==n){n.return=t.return,is=n;break}is=t.return}}function Os(e){for(;null!==is;){var t=is;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{fs(4,t)}catch(Tt){Mu(t,n,Tt)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var a=t.return;try{r.componentDidMount()}catch(Tt){Mu(t,a,Tt)}}var l=t.return;try{ps(t)}catch(Tt){Mu(t,l,Tt)}break;case 5:var o=t.return;try{ps(t)}catch(Tt){Mu(t,o,Tt)}}}catch(Tt){Mu(t,t.return,Tt)}if(t===e){is=null;break}var i=t.sibling;if(null!==i){i.return=t.return,is=i;break}is=t.return}}var Ts,Ms=Math.ceil,Ls=x.ReactCurrentDispatcher,Rs=x.ReactCurrentOwner,Is=x.ReactCurrentBatchConfig,Ds=0,Fs=null,Us=null,As=0,Bs=0,Ws=Ma(0),$s=0,Vs=null,Hs=0,Qs=0,qs=0,Gs=null,Ks=null,Ys=0,Xs=1/0,Js=null,Zs=!1,eu=null,tu=null,nu=!1,ru=null,au=0,lu=0,ou=null,iu=-1,su=0;function uu(){return 0!==(6&Ds)?at():-1!==iu?iu:iu=at()}function cu(e){return 0===(1&e.mode)?1:0!==(2&Ds)&&0!==As?As&-As:null!==Nl.transition?(0===su&&(su=St()),su):0!==(e=jt)?e:e=void 0===(e=window.event)?16:ln(e.type)}function du(e,t,n,r){if(50<lu)throw lu=0,ou=null,Error(l(185));Ct(e,n,r),0!==(2&Ds)&&e===Fs||(e===Fs&&(0===(2&Ds)&&(Qs|=n),4===$s&&gu(e,As)),fu(e,r),1===n&&0===Ds&&0===(1&t.mode)&&(Xs=at()+500,Ga&&Xa()))}function fu(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,l=e.pendingLanes;0<l;){var o=31-pt(l),i=1<<o,s=a[o];-1===s?0!==(i&n)&&0===(i&r)||(a[o]=wt(i,t)):s<=t&&(e.expiredLanes|=i),l&=~i}}(e,t);var r=xt(e,e===Fs?As:0);if(0===r)null!==n&&tt(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&tt(n),1===t)0===e.tag?function(e){Ga=!0,Ya(e)}(vu.bind(null,e)):Ya(vu.bind(null,e)),ha((function(){0===(6&Ds)&&Xa()})),n=null;else{switch(Pt(r)){case 1:n=ot;break;case 4:n=it;break;case 16:default:n=st;break;case 536870912:n=ct}n=Fu(n,pu.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function pu(e,t){if(iu=-1,su=0,0!==(6&Ds))throw Error(l(327));var n=e.callbackNode;if(Ou()&&e.callbackNode!==n)return null;var r=xt(e,e===Fs?As:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=Cu(e,r);else{t=r;var a=Ds;Ds|=2;var o=Su();for(Fs===e&&As===t||(Js=null,Xs=at()+500,wu(e,t));;)try{ju();break}catch(Xe){ku(e,Xe)}Rl(),Ls.current=o,Ds=a,null!==Us?t=0:(Fs=null,As=0,t=$s)}if(0!==t){if(2===t&&(0!==(a=kt(e))&&(r=a,t=mu(e,a))),1===t)throw n=Vs,wu(e,0),gu(e,r),fu(e,at()),n;if(6===t)gu(e,r);else{if(a=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],l=a.getSnapshot;a=a.value;try{if(!gr(l(),a))return!1}catch(i){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(a)&&(2===(t=Cu(e,r))&&(0!==(o=kt(e))&&(r=o,t=mu(e,o))),1===t))throw n=Vs,wu(e,0),gu(e,r),fu(e,at()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(l(345));case 2:case 5:zu(e,Ks,Js);break;case 3:if(gu(e,r),(130023424&r)===r&&10<(t=Ys+500-at())){if(0!==xt(e,0))break;if(((a=e.suspendedLanes)&r)!==r){uu(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=fa(zu.bind(null,e,Ks,Js),t);break}zu(e,Ks,Js);break;case 4:if(gu(e,r),(4194240&r)===r)break;for(t=e.eventTimes,a=-1;0<r;){var i=31-pt(r);o=1<<i,(i=t[i])>a&&(a=i),r&=~o}if(r=a,10<(r=(120>(r=at()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Ms(r/1960))-r)){e.timeoutHandle=fa(zu.bind(null,e,Ks,Js),r);break}zu(e,Ks,Js);break;default:throw Error(l(329))}}}return fu(e,at()),e.callbackNode===n?pu.bind(null,e):null}function mu(e,t){var n=Gs;return e.current.memoizedState.isDehydrated&&(wu(e,t).flags|=256),2!==(e=Cu(e,t))&&(t=Ks,Ks=n,null!==t&&hu(t)),e}function hu(e){null===Ks?Ks=e:Ks.push.apply(Ks,e)}function gu(e,t){for(t&=~qs,t&=~Qs,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-pt(t),r=1<<n;e[n]=-1,t&=~r}}function vu(e){if(0!==(6&Ds))throw Error(l(327));Ou();var t=xt(e,0);if(0===(1&t))return fu(e,at()),null;var n=Cu(e,t);if(0!==e.tag&&2===n){var r=kt(e);0!==r&&(t=r,n=mu(e,r))}if(1===n)throw n=Vs,wu(e,0),gu(e,t),fu(e,at()),n;if(6===n)throw Error(l(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,zu(e,Ks,Js),fu(e,at()),null}function yu(e,t){var n=Ds;Ds|=1;try{return e(t)}finally{0===(Ds=n)&&(Xs=at()+500,Ga&&Xa())}}function bu(e){null!==ru&&0===ru.tag&&0===(6&Ds)&&Ou();var t=Ds;Ds|=1;var n=Is.transition,r=jt;try{if(Is.transition=null,jt=1,e)return e()}finally{jt=r,Is.transition=n,0===(6&(Ds=t))&&Xa()}}function xu(){Bs=Ws.current,La(Ws)}function wu(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,pa(n)),null!==Us)for(n=Us.return;null!==n;){var r=n;switch(cl(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&Wa();break;case 3:lo(),La(Fa),La(Da),fo();break;case 5:io(r);break;case 4:lo();break;case 13:case 19:La(so);break;case 10:Il(r.type._context);break;case 22:case 23:xu()}n=n.return}if(Fs=e,Us=e=Wu(e.current,null),As=Bs=t,$s=0,Vs=null,qs=Qs=Hs=0,Ks=Gs=null,null!==Al){for(t=0;t<Al.length;t++)if(null!==(r=(n=Al[t]).interleaved)){n.interleaved=null;var a=r.next,l=n.pending;if(null!==l){var o=l.next;l.next=a,r.next=o}n.pending=r}Al=null}return e}function ku(e,t){for(;;){var n=Us;try{if(Rl(),po.current=ii,bo){for(var r=go.memoizedState;null!==r;){var a=r.queue;null!==a&&(a.pending=null),r=r.next}bo=!1}if(ho=0,yo=vo=go=null,xo=!1,wo=0,Rs.current=null,null===n||null===n.return){$s=1,Vs=t,Us=null;break}e:{var o=e,i=n.return,s=n,u=t;if(t=As,s.flags|=32768,null!==u&&"object"===typeof u&&"function"===typeof u.then){var c=u,d=s,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var m=Ci(i);if(null!==m){m.flags&=-257,Ei(m,i,s,0,t),1&m.mode&&Ni(o,c,t),u=c;var h=(t=m).updateQueue;if(null===h){var g=new Set;g.add(u),t.updateQueue=g}else h.add(u);break e}if(0===(1&t)){Ni(o,c,t),Nu();break e}u=Error(l(426))}else if(pl&&1&s.mode){var v=Ci(i);if(null!==v){0===(65536&v.flags)&&(v.flags|=256),Ei(v,i,s,0,t),Sl(yi(u,s));break e}}o=u=yi(u,s),4!==$s&&($s=2),null===Gs?Gs=[o]:Gs.push(o),o=i;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t,Yl(o,ki(0,u,t));break e;case 1:s=u;var y=o.type,b=o.stateNode;if(0===(128&o.flags)&&("function"===typeof y.getDerivedStateFromError||null!==b&&"function"===typeof b.componentDidCatch&&(null===tu||!tu.has(b)))){o.flags|=65536,t&=-t,o.lanes|=t,Yl(o,Si(o,s,t));break e}}o=o.return}while(null!==o)}_u(n)}catch(x){t=x,Us===n&&null!==n&&(Us=n=n.return);continue}break}}function Su(){var e=Ls.current;return Ls.current=ii,null===e?ii:e}function Nu(){0!==$s&&3!==$s&&2!==$s||($s=4),null===Fs||0===(268435455&Hs)&&0===(268435455&Qs)||gu(Fs,As)}function Cu(e,t){var n=Ds;Ds|=2;var r=Su();for(Fs===e&&As===t||(Js=null,wu(e,t));;)try{Eu();break}catch(Fe){ku(e,Fe)}if(Rl(),Ds=n,Ls.current=r,null!==Us)throw Error(l(261));return Fs=null,As=0,$s}function Eu(){for(;null!==Us;)Pu(Us)}function ju(){for(;null!==Us&&!nt();)Pu(Us)}function Pu(e){var t=Ts(e.alternate,e,Bs);e.memoizedProps=e.pendingProps,null===t?_u(e):Us=t,Rs.current=null}function _u(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=ns(n,t,Bs)))return void(Us=n)}else{if(null!==(n=rs(n,t)))return n.flags&=32767,void(Us=n);if(null===e)return $s=6,void(Us=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Us=t);Us=t=e}while(null!==t);0===$s&&($s=5)}function zu(e,t,n){var r=jt,a=Is.transition;try{Is.transition=null,jt=1,function(e,t,n,r){do{Ou()}while(null!==ru);if(0!==(6&Ds))throw Error(l(327));n=e.finishedWork;var a=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(l(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-pt(n),l=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~l}}(e,o),e===Fs&&(Us=Fs=null,As=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||nu||(nu=!0,Fu(st,(function(){return Ou(),null}))),o=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||o){o=Is.transition,Is.transition=null;var i=jt;jt=1;var s=Ds;Ds|=4,Rs.current=null,function(e,t){if(ua=Zt,kr(e=wr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch(gt){n=null;break e}var i=0,s=-1,u=-1,c=0,d=0,f=e,p=null;t:for(;;){for(var m;f!==n||0!==a&&3!==f.nodeType||(s=i+a),f!==o||0!==r&&3!==f.nodeType||(u=i+r),3===f.nodeType&&(i+=f.nodeValue.length),null!==(m=f.firstChild);)p=f,f=m;for(;;){if(f===e)break t;if(p===n&&++c===a&&(s=i),p===o&&++d===r&&(u=i),null!==(m=f.nextSibling))break;p=(f=p).parentNode}f=m}n=-1===s||-1===u?null:{start:s,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(ca={focusedElem:e,selectionRange:n},Zt=!1,is=t;null!==is;)if(e=(t=is).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,is=e;else for(;null!==is;){t=is;try{var h=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==h){var g=h.memoizedProps,v=h.memoizedState,y=t.stateNode,b=y.getSnapshotBeforeUpdate(t.elementType===t.type?g:di(t.type,g),v);y.__reactInternalSnapshotBeforeUpdate=b}break;case 3:var x=t.stateNode.containerInfo;1===x.nodeType?x.textContent="":9===x.nodeType&&x.documentElement&&x.removeChild(x.documentElement);break;default:throw Error(l(163))}}catch(gt){Mu(t,t.return,gt)}if(null!==(e=t.sibling)){e.return=t.return,is=e;break}is=t.return}h=cs,cs=!1}(e,n),Cs(n,e),Sr(ca),Zt=!!ua,ca=ua=null,e.current=n,js(n,e,a),rt(),Ds=s,jt=i,Is.transition=o}else e.current=n;if(nu&&(nu=!1,ru=e,au=a),o=e.pendingLanes,0===o&&(tu=null),function(e){if(ft&&"function"===typeof ft.onCommitFiberRoot)try{ft.onCommitFiberRoot(dt,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),fu(e,at()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)a=t[n],r(a.value,{componentStack:a.stack,digest:a.digest});if(Zs)throw Zs=!1,e=eu,eu=null,e;0!==(1&au)&&0!==e.tag&&Ou(),o=e.pendingLanes,0!==(1&o)?e===ou?lu++:(lu=0,ou=e):lu=0,Xa()}(e,t,n,r)}finally{Is.transition=a,jt=r}return null}function Ou(){if(null!==ru){var e=Pt(au),t=Is.transition,n=jt;try{if(Is.transition=null,jt=16>e?16:e,null===ru)var r=!1;else{if(e=ru,ru=null,au=0,0!==(6&Ds))throw Error(l(331));var a=Ds;for(Ds|=4,is=e.current;null!==is;){var o=is,i=o.child;if(0!==(16&is.flags)){var s=o.deletions;if(null!==s){for(var u=0;u<s.length;u++){var c=s[u];for(is=c;null!==is;){var d=is;switch(d.tag){case 0:case 11:case 15:ds(8,d,o)}var f=d.child;if(null!==f)f.return=d,is=f;else for(;null!==is;){var p=(d=is).sibling,m=d.return;if(ms(d),d===c){is=null;break}if(null!==p){p.return=m,is=p;break}is=m}}}var h=o.alternate;if(null!==h){var g=h.child;if(null!==g){h.child=null;do{var v=g.sibling;g.sibling=null,g=v}while(null!==g)}}is=o}}if(0!==(2064&o.subtreeFlags)&&null!==i)i.return=o,is=i;else e:for(;null!==is;){if(0!==(2048&(o=is).flags))switch(o.tag){case 0:case 11:case 15:ds(9,o,o.return)}var y=o.sibling;if(null!==y){y.return=o.return,is=y;break e}is=o.return}}var b=e.current;for(is=b;null!==is;){var x=(i=is).child;if(0!==(2064&i.subtreeFlags)&&null!==x)x.return=i,is=x;else e:for(i=b;null!==is;){if(0!==(2048&(s=is).flags))try{switch(s.tag){case 0:case 11:case 15:fs(9,s)}}catch(k){Mu(s,s.return,k)}if(s===i){is=null;break e}var w=s.sibling;if(null!==w){w.return=s.return,is=w;break e}is=s.return}}if(Ds=a,Xa(),ft&&"function"===typeof ft.onPostCommitFiberRoot)try{ft.onPostCommitFiberRoot(dt,e)}catch(k){}r=!0}return r}finally{jt=n,Is.transition=t}}return!1}function Tu(e,t,n){e=Gl(e,t=ki(0,t=yi(n,t),1),1),t=uu(),null!==e&&(Ct(e,1,t),fu(e,t))}function Mu(e,t,n){if(3===e.tag)Tu(e,e,n);else for(;null!==t;){if(3===t.tag){Tu(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===tu||!tu.has(r))){t=Gl(t,e=Si(t,e=yi(n,e),1),1),e=uu(),null!==t&&(Ct(t,1,e),fu(t,e));break}}t=t.return}}function Lu(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=uu(),e.pingedLanes|=e.suspendedLanes&n,Fs===e&&(As&n)===n&&(4===$s||3===$s&&(130023424&As)===As&&500>at()-Ys?wu(e,0):qs|=n),fu(e,t)}function Ru(e,t){0===t&&(0===(1&e.mode)?t=1:(t=yt,0===(130023424&(yt<<=1))&&(yt=4194304)));var n=uu();null!==(e=$l(e,t))&&(Ct(e,t,n),fu(e,n))}function Iu(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Ru(e,n)}function Du(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(l(314))}null!==r&&r.delete(t),Ru(e,n)}function Fu(e,t){return et(e,t)}function Uu(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Au(e,t,n,r){return new Uu(e,t,n,r)}function Bu(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Wu(e,t){var n=e.alternate;return null===n?((n=Au(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function $u(e,t,n,r,a,o){var i=2;if(r=e,"function"===typeof e)Bu(e)&&(i=1);else if("string"===typeof e)i=5;else e:switch(e){case S:return Vu(n.children,a,o,t);case N:i=8,a|=8;break;case C:return(e=Au(12,n,t,2|a)).elementType=C,e.lanes=o,e;case _:return(e=Au(13,n,t,a)).elementType=_,e.lanes=o,e;case z:return(e=Au(19,n,t,a)).elementType=z,e.lanes=o,e;case M:return Hu(n,a,o,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case E:i=10;break e;case j:i=9;break e;case P:i=11;break e;case O:i=14;break e;case T:i=16,r=null;break e}throw Error(l(130,null==e?e:typeof e,""))}return(t=Au(i,n,t,a)).elementType=e,t.type=r,t.lanes=o,t}function Vu(e,t,n,r){return(e=Au(7,e,r,t)).lanes=n,e}function Hu(e,t,n,r){return(e=Au(22,e,r,t)).elementType=M,e.lanes=n,e.stateNode={isHidden:!1},e}function Qu(e,t,n){return(e=Au(6,e,null,t)).lanes=n,e}function qu(e,t,n){return(t=Au(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Gu(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Nt(0),this.expirationTimes=Nt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Nt(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function Ku(e,t,n,r,a,l,o,i,s){return e=new Gu(e,t,n,i,s),1===t?(t=1,!0===l&&(t|=8)):t=0,l=Au(3,null,null,t),e.current=l,l.stateNode=e,l.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Hl(l),e}function Yu(e){if(!e)return Ia;e:{if(Qe(e=e._reactInternals)!==e||1!==e.tag)throw Error(l(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ba(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(l(171))}if(1===e.tag){var n=e.type;if(Ba(n))return Va(e,n,t)}return t}function Xu(e,t,n,r,a,l,o,i,s){return(e=Ku(n,r,!0,e,0,l,0,i,s)).context=Yu(null),n=e.current,(l=ql(r=uu(),a=cu(n))).callback=void 0!==t&&null!==t?t:null,Gl(n,l,a),e.current.lanes=a,Ct(e,a,r),fu(e,r),e}function Ju(e,t,n,r){var a=t.current,l=uu(),o=cu(a);return n=Yu(n),null===t.context?t.context=n:t.pendingContext=n,(t=ql(l,o)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Gl(a,t,o))&&(du(e,a,o,l),Kl(e,a,o)),o}function Zu(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function ec(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function tc(e,t){ec(e,t),(e=e.alternate)&&ec(e,t)}Ts=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Fa.current)Pi=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return Pi=!1,function(e,t,n){switch(t.tag){case 3:Fi(t),kl();break;case 5:oo(t);break;case 1:Ba(t.type)&&Ha(t);break;case 4:ao(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;Ra(Ol,r._currentValue),r._currentValue=a;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Ra(so,1&so.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Qi(e,t,n):(Ra(so,1&so.current),null!==(e=Zi(e,t,n))?e.sibling:null);Ra(so,1&so.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Xi(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),Ra(so,so.current),r)break;return null;case 22:case 23:return t.lanes=0,Mi(e,t,n)}return Zi(e,t,n)}(e,t,n);Pi=0!==(131072&e.flags)}else Pi=!1,pl&&0!==(1048576&t.flags)&&sl(t,tl,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Ji(e,t),e=t.pendingProps;var a=Aa(t,Da.current);Fl(t,n),a=Co(null,t,r,e,a,n);var o=Eo();return t.flags|=1,"object"===typeof a&&null!==a&&"function"===typeof a.render&&void 0===a.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ba(r)?(o=!0,Ha(t)):o=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,Hl(t),a.updater=pi,t.stateNode=a,a._reactInternals=t,vi(t,r,e,n),t=Di(null,t,r,!0,o,n)):(t.tag=0,pl&&o&&ul(t),_i(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Ji(e,t),e=t.pendingProps,r=(a=r._init)(r._payload),t.type=r,a=t.tag=function(e){if("function"===typeof e)return Bu(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===P)return 11;if(e===O)return 14}return 2}(r),e=di(r,e),a){case 0:t=Ri(null,t,r,e,n);break e;case 1:t=Ii(null,t,r,e,n);break e;case 11:t=zi(null,t,r,e,n);break e;case 14:t=Oi(null,t,r,di(r.type,e),n);break e}throw Error(l(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,Ri(e,t,r,a=t.elementType===r?a:di(r,a),n);case 1:return r=t.type,a=t.pendingProps,Ii(e,t,r,a=t.elementType===r?a:di(r,a),n);case 3:e:{if(Fi(t),null===e)throw Error(l(387));r=t.pendingProps,a=(o=t.memoizedState).element,Ql(e,t),Xl(t,r,null,n);var i=t.memoizedState;if(r=i.element,o.isDehydrated){if(o={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=o,t.memoizedState=o,256&t.flags){t=Ui(e,t,r,n,a=yi(Error(l(423)),t));break e}if(r!==a){t=Ui(e,t,r,n,a=yi(Error(l(424)),t));break e}for(fl=ya(t.stateNode.containerInfo.firstChild),dl=t,pl=!0,ml=null,n=zl(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(kl(),r===a){t=Zi(e,t,n);break e}_i(e,t,r,n)}t=t.child}return t;case 5:return oo(t),null===e&&yl(t),r=t.type,a=t.pendingProps,o=null!==e?e.memoizedProps:null,i=a.children,da(r,a)?i=null:null!==o&&da(r,o)&&(t.flags|=32),Li(e,t),_i(e,t,i,n),t.child;case 6:return null===e&&yl(t),null;case 13:return Qi(e,t,n);case 4:return ao(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=_l(t,null,r,n):_i(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,zi(e,t,r,a=t.elementType===r?a:di(r,a),n);case 7:return _i(e,t,t.pendingProps,n),t.child;case 8:case 12:return _i(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,o=t.memoizedProps,i=a.value,Ra(Ol,r._currentValue),r._currentValue=i,null!==o)if(gr(o.value,i)){if(o.children===a.children&&!Fa.current){t=Zi(e,t,n);break e}}else for(null!==(o=t.child)&&(o.return=t);null!==o;){var s=o.dependencies;if(null!==s){i=o.child;for(var u=s.firstContext;null!==u;){if(u.context===r){if(1===o.tag){(u=ql(-1,n&-n)).tag=2;var c=o.updateQueue;if(null!==c){var d=(c=c.shared).pending;null===d?u.next=u:(u.next=d.next,d.next=u),c.pending=u}}o.lanes|=n,null!==(u=o.alternate)&&(u.lanes|=n),Dl(o.return,n,t),s.lanes|=n;break}u=u.next}}else if(10===o.tag)i=o.type===t.type?null:o.child;else if(18===o.tag){if(null===(i=o.return))throw Error(l(341));i.lanes|=n,null!==(s=i.alternate)&&(s.lanes|=n),Dl(i,n,t),i=o.sibling}else i=o.child;if(null!==i)i.return=o;else for(i=o;null!==i;){if(i===t){i=null;break}if(null!==(o=i.sibling)){o.return=i.return,i=o;break}i=i.return}o=i}_i(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,Fl(t,n),r=r(a=Ul(a)),t.flags|=1,_i(e,t,r,n),t.child;case 14:return a=di(r=t.type,t.pendingProps),Oi(e,t,r,a=di(r.type,a),n);case 15:return Ti(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:di(r,a),Ji(e,t),t.tag=1,Ba(r)?(e=!0,Ha(t)):e=!1,Fl(t,n),hi(t,r,a),vi(t,r,a,n),Di(null,t,r,!0,e,n);case 19:return Xi(e,t,n);case 22:return Mi(e,t,n)}throw Error(l(156,t.tag))};var nc="function"===typeof reportError?reportError:function(e){console.error(e)};function rc(e){this._internalRoot=e}function ac(e){this._internalRoot=e}function lc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function oc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function ic(){}function sc(e,t,n,r,a){var l=n._reactRootContainer;if(l){var o=l;if("function"===typeof a){var i=a;a=function(){var e=Zu(o);i.call(e)}}Ju(t,o,e,a)}else o=function(e,t,n,r,a){if(a){if("function"===typeof r){var l=r;r=function(){var e=Zu(o);l.call(e)}}var o=Xu(t,r,e,0,null,!1,0,"",ic);return e._reactRootContainer=o,e[Sa]=o.current,Xr(8===e.nodeType?e.parentNode:e),bu(),o}for(;a=e.lastChild;)e.removeChild(a);if("function"===typeof r){var i=r;r=function(){var e=Zu(s);i.call(e)}}var s=Ku(e,0,!1,null,0,!1,0,"",ic);return e._reactRootContainer=s,e[Sa]=s.current,Xr(8===e.nodeType?e.parentNode:e),bu((function(){Ju(t,s,n,r)})),s}(n,t,e,a,r);return Zu(o)}ac.prototype.render=rc.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(l(409));Ju(e,t,null,null)},ac.prototype.unmount=rc.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;bu((function(){Ju(null,e,null,null)})),t[Sa]=null}},ac.prototype.unstable_scheduleHydration=function(e){if(e){var t=Mt();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Wt.length&&0!==t&&t<Wt[n].priority;n++);Wt.splice(n,0,e),0===n&&Qt(e)}},_t=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=bt(t.pendingLanes);0!==n&&(Et(t,1|n),fu(t,at()),0===(6&Ds)&&(Xs=at()+500,Xa()))}break;case 13:bu((function(){var t=$l(e,1);if(null!==t){var n=uu();du(t,e,1,n)}})),tc(e,1)}},zt=function(e){if(13===e.tag){var t=$l(e,134217728);if(null!==t)du(t,e,134217728,uu());tc(e,134217728)}},Ot=function(e){if(13===e.tag){var t=cu(e),n=$l(e,t);if(null!==n)du(n,e,t,uu());tc(e,t)}},Mt=function(){return jt},Lt=function(e,t){var n=jt;try{return jt=e,t()}finally{jt=n}},we=function(e,t,n){switch(t){case"input":if(J(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=za(r);if(!a)throw Error(l(90));q(r),J(r,a)}}}break;case"textarea":le(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},je=yu,Pe=bu;var uc={usingClientEntryPoint:!1,Events:[Pa,_a,za,Ce,Ee,yu]},cc={findFiberByHostInstance:ja,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},dc={bundleType:cc.bundleType,version:cc.version,rendererPackageName:cc.rendererPackageName,rendererConfig:cc.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:x.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=Je(e))?null:e.stateNode},findFiberByHostInstance:cc.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var fc=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!fc.isDisabled&&fc.supportsFiber)try{dt=fc.inject(dc),ft=fc}catch(Be){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=uc,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!lc(t))throw Error(l(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:k,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!lc(e))throw Error(l(299));var n=!1,r="",a=nc;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(a=t.onRecoverableError)),t=Ku(e,1,!1,null,0,n,0,r,a),e[Sa]=t.current,Xr(8===e.nodeType?e.parentNode:e),new rc(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(l(188));throw e=Object.keys(e).join(","),Error(l(268,e))}return e=null===(e=Je(t))?null:e.stateNode},t.flushSync=function(e){return bu(e)},t.hydrate=function(e,t,n){if(!oc(t))throw Error(l(200));return sc(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!lc(e))throw Error(l(405));var r=null!=n&&n.hydratedSources||null,a=!1,o="",i=nc;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(a=!0),void 0!==n.identifierPrefix&&(o=n.identifierPrefix),void 0!==n.onRecoverableError&&(i=n.onRecoverableError)),t=Xu(t,null,e,1,null!=n?n:null,a,0,o,i),e[Sa]=t.current,Xr(e),r)for(e=0;e<r.length;e++)a=(a=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new ac(t)},t.render=function(e,t,n){if(!oc(t))throw Error(l(200));return sc(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!oc(e))throw Error(l(40));return!!e._reactRootContainer&&(bu((function(){sc(null,null,e,!1,(function(){e._reactRootContainer=null,e[Sa]=null}))})),!0)},t.unstable_batchedUpdates=yu,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!oc(n))throw Error(l(200));if(null==e||void 0===e._reactInternals)throw Error(l(38));return sc(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},853:(e,t,n)=>{e.exports=n(234)},950:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(730)}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var l=t[r]={exports:{}};return e[r](l,l.exports,n),l.exports}(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;n.t=function(r,a){if(1&a&&(r=this(r)),8&a)return r;if("object"===typeof r&&r){if(4&a&&r.__esModule)return r;if(16&a&&"function"===typeof r.then)return r}var l=Object.create(null);n.r(l);var o={};e=e||[null,t({}),t([]),t(t)];for(var i=2&a&&r;"object"==typeof i&&!~e.indexOf(i);i=t(i))Object.getOwnPropertyNames(i).forEach((e=>o[e]=()=>r[e]));return o.default=()=>r,n.d(l,o),l}})(),n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r,a=n(43),l=n.t(a,2),o=n(391),i=n(950),s=n.t(i,2);function u(){return u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},u.apply(this,arguments)}!function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(r||(r={}));const c="popstate";function d(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function f(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(Fe){}}}function p(e,t){return{usr:e.state,key:e.key,idx:t}}function m(e,t,n,r){return void 0===n&&(n=null),u({pathname:"string"===typeof e?e:e.pathname,search:"",hash:""},"string"===typeof t?g(t):t,{state:n,key:t&&t.key||r||Math.random().toString(36).substr(2,8)})}function h(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function g(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function v(e,t,n,a){void 0===a&&(a={});let{window:l=document.defaultView,v5Compat:o=!1}=a,i=l.history,s=r.Pop,f=null,g=v();function v(){return(i.state||{idx:null}).idx}function y(){s=r.Pop;let e=v(),t=null==e?null:e-g;g=e,f&&f({action:s,location:x.location,delta:t})}function b(e){let t="null"!==l.location.origin?l.location.origin:l.location.href,n="string"===typeof e?e:h(e);return n=n.replace(/ $/,"%20"),d(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}null==g&&(g=0,i.replaceState(u({},i.state,{idx:g}),""));let x={get action(){return s},get location(){return e(l,i)},listen(e){if(f)throw new Error("A history only accepts one active listener");return l.addEventListener(c,y),f=e,()=>{l.removeEventListener(c,y),f=null}},createHref:e=>t(l,e),createURL:b,encodeLocation(e){let t=b(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){s=r.Push;let a=m(x.location,e,t);n&&n(a,e),g=v()+1;let u=p(a,g),c=x.createHref(a);try{i.pushState(u,"",c)}catch(d){if(d instanceof DOMException&&"DataCloneError"===d.name)throw d;l.location.assign(c)}o&&f&&f({action:s,location:x.location,delta:1})},replace:function(e,t){s=r.Replace;let a=m(x.location,e,t);n&&n(a,e),g=v();let l=p(a,g),u=x.createHref(a);i.replaceState(l,"",u),o&&f&&f({action:s,location:x.location,delta:0})},go:e=>i.go(e)};return x}var y;!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(y||(y={}));new Set(["lazy","caseSensitive","path","id","index","children"]);function b(e,t,n){return void 0===n&&(n="/"),x(e,t,n,!1)}function x(e,t,n,r){let a=L(("string"===typeof t?g(t):t).pathname||"/",n);if(null==a)return null;let l=w(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every(((e,n)=>e===t[n]));return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(l);let o=null;for(let i=0;null==o&&i<l.length;++i){let e=M(a);o=O(l[i],e,r)}return o}function w(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===r&&(r="");let a=(e,a,l)=>{let o={relativePath:void 0===l?e.path||"":l,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};o.relativePath.startsWith("/")&&(d(o.relativePath.startsWith(r),'Absolute route path "'+o.relativePath+'" nested under path "'+r+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),o.relativePath=o.relativePath.slice(r.length));let i=U([r,o.relativePath]),s=n.concat(o);e.children&&e.children.length>0&&(d(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+i+'".'),w(e.children,t,s,i)),(null!=e.path||e.index)&&t.push({path:i,score:z(i,e.index),routesMeta:s})};return e.forEach(((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let r of k(e.path))a(e,t,r);else a(e,t)})),t}function k(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),l=n.replace(/\?$/,"");if(0===r.length)return a?[l,""]:[l];let o=k(r.join("/")),i=[];return i.push(...o.map((e=>""===e?l:[l,e].join("/")))),a&&i.push(...o),i.map((t=>e.startsWith("/")&&""===t?"/":t))}const S=/^:[\w-]+$/,N=3,C=2,E=1,j=10,P=-2,_=e=>"*"===e;function z(e,t){let n=e.split("/"),r=n.length;return n.some(_)&&(r+=P),t&&(r+=C),n.filter((e=>!_(e))).reduce(((e,t)=>e+(S.test(t)?N:""===t?E:j)),r)}function O(e,t,n){void 0===n&&(n=!1);let{routesMeta:r}=e,a={},l="/",o=[];for(let i=0;i<r.length;++i){let e=r[i],s=i===r.length-1,u="/"===l?t:t.slice(l.length)||"/",c=T({path:e.relativePath,caseSensitive:e.caseSensitive,end:s},u),d=e.route;if(!c&&s&&n&&!r[r.length-1].route.index&&(c=T({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},u)),!c)return null;Object.assign(a,c.params),o.push({params:a,pathname:U([l,c.pathname]),pathnameBase:A(U([l,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(l=U([l,c.pathnameBase]))}return o}function T(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);f("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))");let l=new RegExp(a,t?void 0:"i");return[l,r]}(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let l=a[0],o=l.replace(/(.)\/+$/,"$1"),i=a.slice(1);return{params:r.reduce(((e,t,n)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=i[n]||"";o=l.slice(0,l.length-e.length).replace(/(.)\/+$/,"$1")}const s=i[n];return e[r]=a&&!s?void 0:(s||"").replace(/%2F/g,"/"),e}),{}),pathname:l,pathnameBase:o,pattern:e}}function M(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return f(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function L(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function R(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function I(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}function D(e,t){let n=I(e);return t?n.map(((e,t)=>t===n.length-1?e.pathname:e.pathnameBase)):n.map((e=>e.pathnameBase))}function F(e,t,n,r){let a;void 0===r&&(r=!1),"string"===typeof e?a=g(e):(a=u({},e),d(!a.pathname||!a.pathname.includes("?"),R("?","pathname","search",a)),d(!a.pathname||!a.pathname.includes("#"),R("#","pathname","hash",a)),d(!a.search||!a.search.includes("#"),R("#","search","hash",a)));let l,o=""===e||""===a.pathname,i=o?"/":a.pathname;if(null==i)l=n;else{let e=t.length-1;if(!r&&i.startsWith("..")){let t=i.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}l=e>=0?t[e]:"/"}let s=function(e,t){void 0===t&&(t="/");let{pathname:n,search:r="",hash:a=""}="string"===typeof e?g(e):e,l=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:l,search:B(r),hash:W(a)}}(a,l),c=i&&"/"!==i&&i.endsWith("/"),f=(o||"."===i)&&n.endsWith("/");return s.pathname.endsWith("/")||!c&&!f||(s.pathname+="/"),s}const U=e=>e.join("/").replace(/\/\/+/g,"/"),A=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),B=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",W=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";Error;function $(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}const V=["post","put","patch","delete"],H=(new Set(V),["get",...V]);new Set(H),new Set([301,302,303,307,308]),new Set([307,308]);Symbol("deferred");function Q(){return Q=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Q.apply(this,arguments)}const q=a.createContext(null);const G=a.createContext(null);const K=a.createContext(null);const Y=a.createContext(null);const X=a.createContext({outlet:null,matches:[],isDataRoute:!1});const J=a.createContext(null);function Z(){return null!=a.useContext(Y)}function ee(){return Z()||d(!1),a.useContext(Y).location}function te(e){a.useContext(K).static||a.useLayoutEffect(e)}function ne(){let{isDataRoute:e}=a.useContext(X);return e?function(){let{router:e}=fe(ce.UseNavigateStable),t=me(de.UseNavigateStable),n=a.useRef(!1);return te((()=>{n.current=!0})),a.useCallback((function(r,a){void 0===a&&(a={}),n.current&&("number"===typeof r?e.navigate(r):e.navigate(r,Q({fromRouteId:t},a)))}),[e,t])}():function(){Z()||d(!1);let e=a.useContext(q),{basename:t,future:n,navigator:r}=a.useContext(K),{matches:l}=a.useContext(X),{pathname:o}=ee(),i=JSON.stringify(D(l,n.v7_relativeSplatPath)),s=a.useRef(!1);return te((()=>{s.current=!0})),a.useCallback((function(n,a){if(void 0===a&&(a={}),!s.current)return;if("number"===typeof n)return void r.go(n);let l=F(n,JSON.parse(i),o,"path"===a.relative);null==e&&"/"!==t&&(l.pathname="/"===l.pathname?t:U([t,l.pathname])),(a.replace?r.replace:r.push)(l,a.state,a)}),[t,r,i,o,e])}()}function re(e,t){let{relative:n}=void 0===t?{}:t,{future:r}=a.useContext(K),{matches:l}=a.useContext(X),{pathname:o}=ee(),i=JSON.stringify(D(l,r.v7_relativeSplatPath));return a.useMemo((()=>F(e,JSON.parse(i),o,"path"===n)),[e,i,o,n])}function ae(e,t,n,l){Z()||d(!1);let{navigator:o}=a.useContext(K),{matches:i}=a.useContext(X),s=i[i.length-1],u=s?s.params:{},c=(s&&s.pathname,s?s.pathnameBase:"/");s&&s.route;let f,p=ee();if(t){var m;let e="string"===typeof t?g(t):t;"/"===c||(null==(m=e.pathname)?void 0:m.startsWith(c))||d(!1),f=e}else f=p;let h=f.pathname||"/",v=h;if("/"!==c){let e=c.replace(/^\//,"").split("/");v="/"+h.replace(/^\//,"").split("/").slice(e.length).join("/")}let y=b(e,{pathname:v});let x=ue(y&&y.map((e=>Object.assign({},e,{params:Object.assign({},u,e.params),pathname:U([c,o.encodeLocation?o.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?c:U([c,o.encodeLocation?o.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),i,n,l);return t&&x?a.createElement(Y.Provider,{value:{location:Q({pathname:"/",search:"",hash:"",state:null,key:"default"},f),navigationType:r.Pop}},x):x}function le(){let e=function(){var e;let t=a.useContext(J),n=pe(de.UseRouteError),r=me(de.UseRouteError);if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[r]}(),t=$(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",l={padding:"0.5rem",backgroundColor:r};return a.createElement(a.Fragment,null,a.createElement("h2",null,"Unexpected Application Error!"),a.createElement("h3",{style:{fontStyle:"italic"}},t),n?a.createElement("pre",{style:l},n):null,null)}const oe=a.createElement(le,null);class ie extends a.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?a.createElement(X.Provider,{value:this.props.routeContext},a.createElement(J.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function se(e){let{routeContext:t,match:n,children:r}=e,l=a.useContext(q);return l&&l.static&&l.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(l.staticContext._deepestRenderedBoundaryId=n.route.id),a.createElement(X.Provider,{value:t},r)}function ue(e,t,n,r){var l;if(void 0===t&&(t=[]),void 0===n&&(n=null),void 0===r&&(r=null),null==e){var o;if(!n)return null;if(n.errors)e=n.matches;else{if(!(null!=(o=r)&&o.v7_partialHydration&&0===t.length&&!n.initialized&&n.matches.length>0))return null;e=n.matches}}let i=e,s=null==(l=n)?void 0:l.errors;if(null!=s){let e=i.findIndex((e=>e.route.id&&void 0!==(null==s?void 0:s[e.route.id])));e>=0||d(!1),i=i.slice(0,Math.min(i.length,e+1))}let u=!1,c=-1;if(n&&r&&r.v7_partialHydration)for(let a=0;a<i.length;a++){let e=i[a];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(c=a),e.route.id){let{loaderData:t,errors:r}=n,a=e.route.loader&&void 0===t[e.route.id]&&(!r||void 0===r[e.route.id]);if(e.route.lazy||a){u=!0,i=c>=0?i.slice(0,c+1):[i[0]];break}}}return i.reduceRight(((e,r,l)=>{let o,d=!1,f=null,p=null;var m;n&&(o=s&&r.route.id?s[r.route.id]:void 0,f=r.route.errorElement||oe,u&&(c<0&&0===l?(m="route-fallback",!1||he[m]||(he[m]=!0),d=!0,p=null):c===l&&(d=!0,p=r.route.hydrateFallbackElement||null)));let h=t.concat(i.slice(0,l+1)),g=()=>{let t;return t=o?f:d?p:r.route.Component?a.createElement(r.route.Component,null):r.route.element?r.route.element:e,a.createElement(se,{match:r,routeContext:{outlet:e,matches:h,isDataRoute:null!=n},children:t})};return n&&(r.route.ErrorBoundary||r.route.errorElement||0===l)?a.createElement(ie,{location:n.location,revalidation:n.revalidation,component:f,error:o,children:g(),routeContext:{outlet:null,matches:h,isDataRoute:!0}}):g()}),null)}var ce=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(ce||{}),de=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(de||{});function fe(e){let t=a.useContext(q);return t||d(!1),t}function pe(e){let t=a.useContext(G);return t||d(!1),t}function me(e){let t=function(){let e=a.useContext(X);return e||d(!1),e}(),n=t.matches[t.matches.length-1];return n.route.id||d(!1),n.route.id}const he={};function ge(e,t){null==e||e.v7_startTransition,void 0===(null==e?void 0:e.v7_relativeSplatPath)&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}l.startTransition;function ve(e){let{to:t,replace:n,state:r,relative:l}=e;Z()||d(!1);let{future:o,static:i}=a.useContext(K),{matches:s}=a.useContext(X),{pathname:u}=ee(),c=ne(),f=F(t,D(s,o.v7_relativeSplatPath),u,"path"===l),p=JSON.stringify(f);return a.useEffect((()=>c(JSON.parse(p),{replace:n,state:r,relative:l})),[c,p,l,n,r]),null}function ye(e){d(!1)}function be(e){let{basename:t="/",children:n=null,location:l,navigationType:o=r.Pop,navigator:i,static:s=!1,future:u}=e;Z()&&d(!1);let c=t.replace(/^\/*/,"/"),f=a.useMemo((()=>({basename:c,navigator:i,static:s,future:Q({v7_relativeSplatPath:!1},u)})),[c,u,i,s]);"string"===typeof l&&(l=g(l));let{pathname:p="/",search:m="",hash:h="",state:v=null,key:y="default"}=l,b=a.useMemo((()=>{let e=L(p,c);return null==e?null:{location:{pathname:e,search:m,hash:h,state:v,key:y},navigationType:o}}),[c,p,m,h,v,y,o]);return null==b?null:a.createElement(K.Provider,{value:f},a.createElement(Y.Provider,{children:n,value:b}))}function xe(e){let{children:t,location:n}=e;return ae(we(t),n)}new Promise((()=>{}));a.Component;function we(e,t){void 0===t&&(t=[]);let n=[];return a.Children.forEach(e,((e,r)=>{if(!a.isValidElement(e))return;let l=[...t,r];if(e.type===a.Fragment)return void n.push.apply(n,we(e.props.children,l));e.type!==ye&&d(!1),e.props.index&&e.props.children&&d(!1);let o={id:e.props.id||l.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(o.children=we(e.props.children,l)),n.push(o)})),n}function ke(){return ke=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ke.apply(this,arguments)}function Se(e,t){if(null==e)return{};var n,r,a={},l=Object.keys(e);for(r=0;r<l.length;r++)n=l[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);const Ne=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"];try{window.__reactRouterVersion="6"}catch(Fe){}new Map;const Ce=l.startTransition;s.flushSync,l.useId;function Ee(e){let{basename:t,children:n,future:r,window:l}=e,o=a.useRef();var i;null==o.current&&(o.current=(void 0===(i={window:l,v5Compat:!0})&&(i={}),v((function(e,t){let{pathname:n,search:r,hash:a}=e.location;return m("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"===typeof t?t:h(t)}),null,i)));let s=o.current,[u,c]=a.useState({action:s.action,location:s.location}),{v7_startTransition:d}=r||{},f=a.useCallback((e=>{d&&Ce?Ce((()=>c(e))):c(e)}),[c,d]);return a.useLayoutEffect((()=>s.listen(f)),[s,f]),a.useEffect((()=>ge(r)),[r]),a.createElement(be,{basename:t,children:n,location:u.location,navigationType:u.action,navigator:s,future:r})}const je="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement,Pe=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,_e=a.forwardRef((function(e,t){let n,{onClick:r,relative:l,reloadDocument:o,replace:i,state:s,target:u,to:c,preventScrollReset:f,viewTransition:p}=e,m=Se(e,Ne),{basename:g}=a.useContext(K),v=!1;if("string"===typeof c&&Pe.test(c)&&(n=c,je))try{let e=new URL(window.location.href),t=c.startsWith("//")?new URL(e.protocol+c):new URL(c),n=L(t.pathname,g);t.origin===e.origin&&null!=n?c=n+t.search+t.hash:v=!0}catch(Fe){}let y=function(e,t){let{relative:n}=void 0===t?{}:t;Z()||d(!1);let{basename:r,navigator:l}=a.useContext(K),{hash:o,pathname:i,search:s}=re(e,{relative:n}),u=i;return"/"!==r&&(u="/"===i?r:U([r,i])),l.createHref({pathname:u,search:s,hash:o})}(c,{relative:l}),b=function(e,t){let{target:n,replace:r,state:l,preventScrollReset:o,relative:i,viewTransition:s}=void 0===t?{}:t,u=ne(),c=ee(),d=re(e,{relative:i});return a.useCallback((t=>{if(function(e,t){return 0===e.button&&(!t||"_self"===t)&&!function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)}(t,n)){t.preventDefault();let n=void 0!==r?r:h(c)===h(d);u(e,{replace:n,state:l,preventScrollReset:o,relative:i,viewTransition:s})}}),[c,u,d,r,l,n,e,o,i,s])}(c,{replace:i,state:s,target:u,preventScrollReset:f,relative:l,viewTransition:p});return a.createElement("a",ke({},m,{href:n||y,onClick:v||o?r:function(e){r&&r(e),e.defaultPrevented||b(e)},ref:t,target:u}))}));var ze,Oe;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(ze||(ze={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}(Oe||(Oe={}));function Te(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}function Me(e){return Me="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Me(e)}function Le(e){var t=function(e,t){if("object"!=Me(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Me(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Me(t)?t:t+""}function Re(e,t,n){return(t=Le(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ie(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function De(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ie(Object(n),!0).forEach((function(t){Re(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ie(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}let Fe={data:""},Ue=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||Fe,Ae=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,Be=/\/\*[^]*?\*\/|  +/g,We=/\n+/g,$e=(e,t)=>{let n="",r="",a="";for(let l in e){let o=e[l];"@"==l[0]?"i"==l[1]?n=l+" "+o+";":r+="f"==l[1]?$e(o,l):l+"{"+$e(o,"k"==l[1]?"":t)+"}":"object"==typeof o?r+=$e(o,t?t.replace(/([^,])+/g,(e=>l.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,(t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)))):l):null!=o&&(l=/^--/.test(l)?l:l.replace(/[A-Z]/g,"-$&").toLowerCase(),a+=$e.p?$e.p(l,o):l+":"+o+";")}return n+(t&&a?t+"{"+a+"}":a)+r},Ve={},He=e=>{if("object"==typeof e){let t="";for(let n in e)t+=n+He(e[n]);return t}return e},Qe=(e,t,n,r,a)=>{let l=He(e),o=Ve[l]||(Ve[l]=(e=>{let t=0,n=11;for(;t<e.length;)n=101*n+e.charCodeAt(t++)>>>0;return"go"+n})(l));if(!Ve[o]){let t=l!==e?e:(e=>{let t,n,r=[{}];for(;t=Ae.exec(e.replace(Be,""));)t[4]?r.shift():t[3]?(n=t[3].replace(We," ").trim(),r.unshift(r[0][n]=r[0][n]||{})):r[0][t[1]]=t[2].replace(We," ").trim();return r[0]})(e);Ve[o]=$e(a?{["@keyframes "+o]:t}:t,n?"":"."+o)}let i=n&&Ve.g?Ve.g:null;return n&&(Ve.g=Ve[o]),((e,t,n,r)=>{r?t.data=t.data.replace(r,e):-1===t.data.indexOf(e)&&(t.data=n?e+t.data:t.data+e)})(Ve[o],t,r,i),o};function qe(e){let t=this||{},n=e.call?e(t.p):e;return Qe(n.unshift?n.raw?((e,t,n)=>e.reduce(((e,r,a)=>{let l=t[a];if(l&&l.call){let e=l(n),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;l=t?"."+t:e&&"object"==typeof e?e.props?"":$e(e,""):!1===e?"":e}return e+r+(null==l?"":l)}),""))(n,[].slice.call(arguments,1),t.p):n.reduce(((e,n)=>Object.assign(e,n&&n.call?n(t.p):n)),{}):n,Ue(t.target),t.g,t.o,t.k)}qe.bind({g:1});let Ge,Ke,Ye,Xe=qe.bind({k:1});function Je(e,t){let n=this||{};return function(){let r=arguments;function a(l,o){let i=Object.assign({},l),s=i.className||a.className;n.p=Object.assign({theme:Ke&&Ke()},i),n.o=/ *go\d+/.test(s),i.className=qe.apply(n,r)+(s?" "+s:""),t&&(i.ref=o);let u=e;return e[0]&&(u=i.as||e,delete i.as),Ye&&u[0]&&Ye(i),Ge(u,i)}return t?t(a):a}}var Ze,et,tt,nt,rt,at,lt,ot,it,st,ut,ct,dt,ft,pt,mt,ht=(e,t)=>(e=>"function"==typeof e)(e)?e(t):e,gt=(()=>{let e=0;return()=>(++e).toString()})(),vt=(()=>{let e;return()=>{if(void 0===e&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})(),yt=(e,t)=>{switch(t.type){case 0:return De(De({},e),{},{toasts:[t.toast,...e.toasts].slice(0,20)});case 1:return De(De({},e),{},{toasts:e.toasts.map((e=>e.id===t.toast.id?De(De({},e),t.toast):e))});case 2:let{toast:n}=t;return yt(e,{type:e.toasts.find((e=>e.id===n.id))?1:0,toast:n});case 3:let{toastId:r}=t;return De(De({},e),{},{toasts:e.toasts.map((e=>e.id===r||void 0===r?De(De({},e),{},{dismissed:!0,visible:!1}):e))});case 4:return void 0===t.toastId?De(De({},e),{},{toasts:[]}):De(De({},e),{},{toasts:e.toasts.filter((e=>e.id!==t.toastId))});case 5:return De(De({},e),{},{pausedAt:t.time});case 6:let a=t.time-(e.pausedAt||0);return De(De({},e),{},{pausedAt:void 0,toasts:e.toasts.map((e=>De(De({},e),{},{pauseDuration:e.pauseDuration+a})))})}},bt=[],xt={toasts:[],pausedAt:void 0},wt=e=>{xt=yt(xt,e),bt.forEach((e=>{e(xt)}))},kt={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},St=e=>(t,n)=>{let r=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"blank",n=arguments.length>2?arguments[2]:void 0;return De(De({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0},n),{},{id:(null==n?void 0:n.id)||gt()})}(t,e,n);return wt({type:2,toast:r}),r.id},Nt=(e,t)=>St("blank")(e,t);Nt.error=St("error"),Nt.success=St("success"),Nt.loading=St("loading"),Nt.custom=St("custom"),Nt.dismiss=e=>{wt({type:3,toastId:e})},Nt.remove=e=>wt({type:4,toastId:e}),Nt.promise=(e,t,n)=>{let r=Nt.loading(t.loading,De(De({},n),null==n?void 0:n.loading));return"function"==typeof e&&(e=e()),e.then((e=>{let a=t.success?ht(t.success,e):void 0;return a?Nt.success(a,De(De({id:r},n),null==n?void 0:n.success)):Nt.dismiss(r),e})).catch((e=>{let a=t.error?ht(t.error,e):void 0;a?Nt.error(a,De(De({id:r},n),null==n?void 0:n.error)):Nt.dismiss(r)})),e};var Ct=(e,t)=>{wt({type:1,toast:{id:e,height:t}})},Et=()=>{wt({type:5,time:Date.now()})},jt=new Map,Pt=e=>{let{toasts:t,pausedAt:n}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[t,n]=(0,a.useState)(xt),r=(0,a.useRef)(xt);(0,a.useEffect)((()=>(r.current!==xt&&n(xt),bt.push(n),()=>{let e=bt.indexOf(n);e>-1&&bt.splice(e,1)})),[]);let l=t.toasts.map((t=>{var n,r,a;return De(De(De(De({},e),e[t.type]),t),{},{removeDelay:t.removeDelay||(null==(n=e[t.type])?void 0:n.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(r=e[t.type])?void 0:r.duration)||(null==e?void 0:e.duration)||kt[t.type],style:De(De(De({},e.style),null==(a=e[t.type])?void 0:a.style),t.style)})}));return De(De({},t),{},{toasts:l})}(e);(0,a.useEffect)((()=>{if(n)return;let e=Date.now(),r=t.map((t=>{if(t.duration===1/0)return;let n=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(!(n<0))return setTimeout((()=>Nt.dismiss(t.id)),n);t.visible&&Nt.dismiss(t.id)}));return()=>{r.forEach((e=>e&&clearTimeout(e)))}}),[t,n]);let r=(0,a.useCallback)((()=>{n&&wt({type:6,time:Date.now()})}),[n]),l=(0,a.useCallback)(((e,n)=>{let{reverseOrder:r=!1,gutter:a=8,defaultPosition:l}=n||{},o=t.filter((t=>(t.position||l)===(e.position||l)&&t.height)),i=o.findIndex((t=>t.id===e.id)),s=o.filter(((e,t)=>t<i&&e.visible)).length;return o.filter((e=>e.visible)).slice(...r?[s+1]:[0,s]).reduce(((e,t)=>e+(t.height||0)+a),0)}),[t]);return(0,a.useEffect)((()=>{t.forEach((e=>{if(e.dismissed)!function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3;if(jt.has(e))return;let n=setTimeout((()=>{jt.delete(e),wt({type:4,toastId:e})}),t);jt.set(e,n)}(e.id,e.removeDelay);else{let t=jt.get(e.id);t&&(clearTimeout(t),jt.delete(e.id))}}))}),[t]),{toasts:t,handlers:{updateHeight:Ct,startPause:Et,endPause:r,calculateOffset:l}}},_t=Xe(Ze||(Ze=Te(["\nfrom {\n  transform: scale(0) rotate(45deg);\n\topacity: 0;\n}\nto {\n transform: scale(1) rotate(45deg);\n  opacity: 1;\n}"]))),zt=Xe(et||(et=Te(["\nfrom {\n  transform: scale(0);\n  opacity: 0;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}"]))),Ot=Xe(tt||(tt=Te(["\nfrom {\n  transform: scale(0) rotate(90deg);\n\topacity: 0;\n}\nto {\n  transform: scale(1) rotate(90deg);\n\topacity: 1;\n}"]))),Tt=Je("div")(nt||(nt=Te(["\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ",";\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: "," 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n\n  &:after,\n  &:before {\n    content: '';\n    animation: "," 0.15s ease-out forwards;\n    animation-delay: 150ms;\n    position: absolute;\n    border-radius: 3px;\n    opacity: 0;\n    background: ",";\n    bottom: 9px;\n    left: 4px;\n    height: 2px;\n    width: 12px;\n  }\n\n  &:before {\n    animation: "," 0.15s ease-out forwards;\n    animation-delay: 180ms;\n    transform: rotate(90deg);\n  }\n"])),(e=>e.primary||"#ff4b4b"),_t,zt,(e=>e.secondary||"#fff"),Ot),Mt=Xe(rt||(rt=Te(["\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n"]))),Lt=Je("div")(at||(at=Te(["\n  width: 12px;\n  height: 12px;\n  box-sizing: border-box;\n  border: 2px solid;\n  border-radius: 100%;\n  border-color: ",";\n  border-right-color: ",";\n  animation: "," 1s linear infinite;\n"])),(e=>e.secondary||"#e0e0e0"),(e=>e.primary||"#616161"),Mt),Rt=Xe(lt||(lt=Te(["\nfrom {\n  transform: scale(0) rotate(45deg);\n\topacity: 0;\n}\nto {\n  transform: scale(1) rotate(45deg);\n\topacity: 1;\n}"]))),It=Xe(ot||(ot=Te(["\n0% {\n\theight: 0;\n\twidth: 0;\n\topacity: 0;\n}\n40% {\n  height: 0;\n\twidth: 6px;\n\topacity: 1;\n}\n100% {\n  opacity: 1;\n  height: 10px;\n}"]))),Dt=Je("div")(it||(it=Te(["\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ",";\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: "," 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n  &:after {\n    content: '';\n    box-sizing: border-box;\n    animation: "," 0.2s ease-out forwards;\n    opacity: 0;\n    animation-delay: 200ms;\n    position: absolute;\n    border-right: 2px solid;\n    border-bottom: 2px solid;\n    border-color: ",";\n    bottom: 6px;\n    left: 6px;\n    height: 10px;\n    width: 6px;\n  }\n"])),(e=>e.primary||"#61d345"),Rt,It,(e=>e.secondary||"#fff")),Ft=Je("div")(st||(st=Te(["\n  position: absolute;\n"]))),Ut=Je("div")(ut||(ut=Te(["\n  position: relative;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-width: 20px;\n  min-height: 20px;\n"]))),At=Xe(ct||(ct=Te(["\nfrom {\n  transform: scale(0.6);\n  opacity: 0.4;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}"]))),Bt=Je("div")(dt||(dt=Te(["\n  position: relative;\n  transform: scale(0.6);\n  opacity: 0.4;\n  min-width: 20px;\n  animation: "," 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n"])),At),Wt=e=>{let{toast:t}=e,{icon:n,type:r,iconTheme:l}=t;return void 0!==n?"string"==typeof n?a.createElement(Bt,null,n):n:"blank"===r?null:a.createElement(Ut,null,a.createElement(Lt,De({},l)),"loading"!==r&&a.createElement(Ft,null,"error"===r?a.createElement(Tt,De({},l)):a.createElement(Dt,De({},l))))},$t=e=>"\n0% {transform: translate3d(0,".concat(-200*e,"%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n"),Vt=e=>"\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,".concat(-150*e,"%,-1px) scale(.6); opacity:0;}\n"),Ht=Je("div")(ft||(ft=Te(["\n  display: flex;\n  align-items: center;\n  background: #fff;\n  color: #363636;\n  line-height: 1.3;\n  will-change: transform;\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);\n  max-width: 350px;\n  pointer-events: auto;\n  padding: 8px 10px;\n  border-radius: 8px;\n"]))),Qt=Je("div")(pt||(pt=Te(["\n  display: flex;\n  justify-content: center;\n  margin: 4px 10px;\n  color: inherit;\n  flex: 1 1 auto;\n  white-space: pre-line;\n"]))),qt=a.memo((e=>{let{toast:t,position:n,style:r,children:l}=e,o=t.height?((e,t)=>{let n=e.includes("top")?1:-1,[r,a]=vt()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[$t(n),Vt(n)];return{animation:t?"".concat(Xe(r)," 0.35s cubic-bezier(.21,1.02,.73,1) forwards"):"".concat(Xe(a)," 0.4s forwards cubic-bezier(.06,.71,.55,1)")}})(t.position||n||"top-center",t.visible):{opacity:0},i=a.createElement(Wt,{toast:t}),s=a.createElement(Qt,De({},t.ariaProps),ht(t.message,t));return a.createElement(Ht,{className:t.className,style:De(De(De({},o),r),t.style)},"function"==typeof l?l({icon:i,message:s}):a.createElement(a.Fragment,null,i,s))}));!function(e,t,n,r){$e.p=t,Ge=e,Ke=n,Ye=r}(a.createElement);var Gt=e=>{let{id:t,className:n,style:r,onHeightUpdate:l,children:o}=e,i=a.useCallback((e=>{if(e){let n=()=>{let n=e.getBoundingClientRect().height;l(t,n)};n(),new MutationObserver(n).observe(e,{subtree:!0,childList:!0,characterData:!0})}}),[t,l]);return a.createElement("div",{ref:i,className:n,style:r},o)},Kt=qe(mt||(mt=Te(["\n  z-index: 9999;\n  > * {\n    pointer-events: auto;\n  }\n"]))),Yt=e=>{let{reverseOrder:t,position:n="top-center",toastOptions:r,gutter:l,children:o,containerStyle:i,containerClassName:s}=e,{toasts:u,handlers:c}=Pt(r);return a.createElement("div",{id:"_rht_toaster",style:De({position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none"},i),className:s,onMouseEnter:c.startPause,onMouseLeave:c.endPause},u.map((e=>{let r=e.position||n,i=((e,t)=>{let n=e.includes("top"),r=n?{top:0}:{bottom:0},a=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return De(De({left:0,right:0,display:"flex",position:"absolute",transition:vt()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:"translateY(".concat(t*(n?1:-1),"px)")},r),a)})(r,c.calculateOffset(e,{reverseOrder:t,gutter:l,defaultPosition:n}));return a.createElement(Gt,{id:e.id,key:e.id,onHeightUpdate:c.updateHeight,className:e.visible?Kt:"",style:i},"custom"===e.type?ht(e.message,e):o?o(e):a.createElement(qt,{toast:e,position:r}))})))};function Xt(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(r=0;r<l.length;r++)n=l[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}var Jt={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const Zt=["color","size","strokeWidth","absoluteStrokeWidth","children"];var en=(e,t)=>{const n=(0,a.forwardRef)(((n,r)=>{let{color:l="currentColor",size:o=24,strokeWidth:i=2,absoluteStrokeWidth:s,children:u}=n,c=Xt(n,Zt);return(0,a.createElement)("svg",De(De({ref:r},Jt),{},{width:o,height:o,stroke:l,strokeWidth:s?24*Number(i)/Number(o):i,className:"lucide lucide-".concat((d=e,d.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()))},c),[...t.map((e=>{let[t,n]=e;return(0,a.createElement)(t,n)})),...(Array.isArray(u)?u:[u])||[]]);var d}));return n.displayName="".concat(e),n};const tn=en("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),nn=en("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),rn=en("Crown",[["path",{d:"m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14",key:"zkxr6b"}]]),an=en("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]),ln=en("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]),on=en("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]]),sn=en("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),un=en("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);var cn=n(579);const dn=()=>{const[e,t]=(0,a.useState)(!1),[n,r]=(0,a.useState)(!1),l=ee(),o=[{name:"Convert",href:"/convert",current:"/convert"===l.pathname},{name:"Chat",href:"/chat",current:"/chat"===l.pathname,comingSoon:!0},{name:"Tools",href:"/tools",current:"/tools"===l.pathname,comingSoon:!0},{name:"Playground",href:"/playground",current:"/playground"===l.pathname,comingSoon:!0}],i=[{name:"Your Profile",href:"/profile",icon:tn},{name:"Settings",href:"/settings",icon:nn},{name:"Upgrade to Pro",href:"/pricing",icon:rn,highlight:!0},{name:"Sign out",href:"/logout",icon:an}];return(0,cn.jsx)("header",{className:"bg-white shadow-lg border-b border-gray-200 sticky top-0 z-50",children:(0,cn.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,cn.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,cn.jsx)("div",{className:"flex items-center",children:(0,cn.jsxs)(_e,{to:"/",className:"flex items-center space-x-2",children:[(0,cn.jsx)("div",{className:"flex items-center justify-center w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg",children:(0,cn.jsx)(ln,{className:"w-5 h-5 text-white"})}),(0,cn.jsxs)("div",{className:"flex flex-col",children:[(0,cn.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"MCPify"}),(0,cn.jsx)("span",{className:"text-xs text-gray-500 -mt-1",children:"API to MCP Converter"})]})]})}),(0,cn.jsx)("nav",{className:"hidden md:flex space-x-1",children:o.map((e=>(0,cn.jsx)("div",{className:"relative",children:e.comingSoon?(0,cn.jsxs)("div",{className:"relative group",children:[(0,cn.jsxs)("span",{className:"text-gray-400 px-3 py-2 text-sm font-medium cursor-not-allowed flex items-center space-x-1",children:[(0,cn.jsx)("span",{children:e.name}),(0,cn.jsx)(on,{className:"w-3 h-3"})]}),(0,cn.jsx)("div",{className:"absolute top-full left-1/2 transform -translate-x-1/2 mt-1 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap",children:"Coming Soon"})]}):(0,cn.jsx)(_e,{to:e.href,className:"px-3 py-2 text-sm font-medium rounded-md transition-colors ".concat(e.current?"bg-blue-100 text-blue-700":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"),children:e.name})},e.name)))}),(0,cn.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,cn.jsxs)(_e,{to:"/pricing",className:"hidden md:flex items-center space-x-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-blue-700 hover:to-purple-700 transition-all",children:[(0,cn.jsx)(rn,{className:"w-4 h-4"}),(0,cn.jsx)("span",{children:"Upgrade"})]}),(0,cn.jsxs)("div",{className:"relative",children:[(0,cn.jsx)("button",{onClick:()=>r(!n),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-900 p-2 rounded-lg hover:bg-gray-100 transition-colors",children:(0,cn.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center",children:(0,cn.jsx)(tn,{className:"w-4 h-4 text-white"})})}),n&&(0,cn.jsx)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50",children:i.map((e=>(0,cn.jsxs)(_e,{to:e.href,className:"flex items-center space-x-2 px-4 py-2 text-sm hover:bg-gray-100 transition-colors ".concat(e.highlight?"text-blue-600 font-medium":"text-gray-700"),onClick:()=>r(!1),children:[(0,cn.jsx)(e.icon,{className:"w-4 h-4"}),(0,cn.jsx)("span",{children:e.name})]},e.name)))})]}),(0,cn.jsx)("button",{onClick:()=>t(!e),className:"md:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100",children:e?(0,cn.jsx)(sn,{className:"w-5 h-5"}):(0,cn.jsx)(un,{className:"w-5 h-5"})})]})]}),e&&(0,cn.jsx)("div",{className:"md:hidden border-t border-gray-200 py-4",children:(0,cn.jsx)("div",{className:"space-y-1",children:o.map((e=>(0,cn.jsx)("div",{children:e.comingSoon?(0,cn.jsxs)("div",{className:"flex items-center justify-between px-3 py-2 text-gray-400",children:[(0,cn.jsx)("span",{className:"text-base font-medium",children:e.name}),(0,cn.jsx)("span",{className:"text-xs bg-gray-100 px-2 py-1 rounded-full",children:"Coming Soon"})]}):(0,cn.jsx)(_e,{to:e.href,className:"block px-3 py-2 text-base font-medium rounded-md ".concat(e.current?"bg-blue-100 text-blue-700":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"),onClick:()=>t(!1),children:e.name})},e.name)))})})]})})},fn=en("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]),pn=en("Bot",[["rect",{width:"18",height:"10",x:"3",y:"11",rx:"2",key:"1ofdy3"}],["circle",{cx:"12",cy:"5",r:"2",key:"f1ur92"}],["path",{d:"M12 7v4",key:"xawao1"}],["line",{x1:"8",x2:"8",y1:"16",y2:"16",key:"h6x27f"}],["line",{x1:"16",x2:"16",y1:"16",y2:"16",key:"5lty7f"}]]),mn=en("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z",key:"3xmgem"}]]),hn=en("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"2",x2:"22",y1:"12",y2:"12",key:"1dnqot"}],["path",{d:"M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z",key:"nb9nel"}]]),gn=en("Rocket",[["path",{d:"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z",key:"m3kijz"}],["path",{d:"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z",key:"1fmvmk"}],["path",{d:"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0",key:"1f8sc4"}],["path",{d:"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5",key:"qeys4"}]]),vn=en("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),yn=()=>{const e=[{icon:ln,title:"Instant Conversion",description:"Convert any OpenAPI specification to MCP server in seconds"},{icon:fn,title:"Production Ready",description:"Generated servers are TypeScript-based and production-ready"},{icon:pn,title:"AI Compatible",description:"Works with Cline, Cursor, Windsurf, and other MCP clients"},{icon:mn,title:"Secure & Reliable",description:"Built with security best practices and error handling"},{icon:hn,title:"Universal Support",description:"Supports any REST API with OpenAPI 3.0+ specification"},{icon:gn,title:"Deploy Anywhere",description:"Deploy to Railway, Vercel, or any Node.js hosting platform"}];return(0,cn.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,cn.jsx)("section",{className:"relative bg-gradient-to-br from-blue-50 via-white to-purple-50 py-20",children:(0,cn.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,cn.jsxs)("div",{className:"text-center",children:[(0,cn.jsxs)("h1",{className:"text-5xl md:text-6xl font-bold text-gray-900 mb-6",children:["Transform APIs into",(0,cn.jsxs)("span",{className:"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:[" ","MCP Servers"]})]}),(0,cn.jsx)("p",{className:"text-xl text-gray-600 mb-8 max-w-3xl mx-auto",children:"Convert any OpenAPI specification into a fully functional MCP (Model Context Protocol) server. Enable AI assistants to interact with your APIs seamlessly."}),(0,cn.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center mb-12",children:[(0,cn.jsxs)(_e,{to:"/convert",className:"inline-flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-lg text-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all shadow-lg",children:[(0,cn.jsx)(ln,{className:"w-5 h-5"}),(0,cn.jsx)("span",{children:"Start Converting"}),(0,cn.jsx)(vn,{className:"w-5 h-5"})]}),(0,cn.jsx)(_e,{to:"/playground",className:"inline-flex items-center space-x-2 bg-white text-gray-700 px-8 py-4 rounded-lg text-lg font-medium hover:bg-gray-50 transition-all border border-gray-300",children:(0,cn.jsx)("span",{children:"View Examples"})})]}),(0,cn.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto",children:[{label:"APIs Converted",value:"1,000+"},{label:"Happy Developers",value:"500+"},{label:"MCP Servers Generated",value:"2,500+"},{label:"Uptime",value:"99.9%"}].map(((e,t)=>(0,cn.jsxs)("div",{className:"text-center",children:[(0,cn.jsx)("div",{className:"text-3xl font-bold text-gray-900 mb-1",children:e.value}),(0,cn.jsx)("div",{className:"text-gray-600",children:e.label})]},t)))})]})})}),(0,cn.jsx)("section",{className:"py-20 bg-gray-50",children:(0,cn.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,cn.jsxs)("div",{className:"text-center mb-16",children:[(0,cn.jsx)("h2",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Why Choose MCPify?"}),(0,cn.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"The most powerful and easy-to-use API to MCP converter in the market"})]}),(0,cn.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:e.map(((e,t)=>(0,cn.jsxs)("div",{className:"bg-white rounded-xl p-8 shadow-sm border border-gray-200 hover:shadow-lg transition-shadow",children:[(0,cn.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center mb-6",children:(0,cn.jsx)(e.icon,{className:"w-6 h-6 text-white"})}),(0,cn.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:e.title}),(0,cn.jsx)("p",{className:"text-gray-600",children:e.description})]},t)))})]})}),(0,cn.jsx)("section",{className:"py-20 bg-white",children:(0,cn.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,cn.jsxs)("div",{className:"text-center mb-16",children:[(0,cn.jsx)("h2",{className:"text-4xl font-bold text-gray-900 mb-4",children:"How It Works"}),(0,cn.jsx)("p",{className:"text-xl text-gray-600",children:"Three simple steps to get your MCP server running"})]}),(0,cn.jsxs)("div",{className:"grid md:grid-cols-3 gap-8",children:[(0,cn.jsxs)("div",{className:"text-center",children:[(0,cn.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,cn.jsx)("span",{className:"text-2xl font-bold text-blue-600",children:"1"})}),(0,cn.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Provide OpenAPI Spec"}),(0,cn.jsx)("p",{className:"text-gray-600",children:"Upload your OpenAPI specification file or provide a URL to your API documentation"})]}),(0,cn.jsxs)("div",{className:"text-center",children:[(0,cn.jsx)("div",{className:"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,cn.jsx)("span",{className:"text-2xl font-bold text-purple-600",children:"2"})}),(0,cn.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Generate MCP Server"}),(0,cn.jsx)("p",{className:"text-gray-600",children:"Our AI-powered converter analyzes your API and generates a complete MCP server"})]}),(0,cn.jsxs)("div",{className:"text-center",children:[(0,cn.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,cn.jsx)("span",{className:"text-2xl font-bold text-green-600",children:"3"})}),(0,cn.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Deploy & Use"}),(0,cn.jsx)("p",{className:"text-gray-600",children:"Download your server, deploy it, and connect it to your favorite AI assistant"})]})]})]})}),(0,cn.jsx)("section",{className:"py-20 bg-gradient-to-r from-blue-600 to-purple-600",children:(0,cn.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,cn.jsx)("h2",{className:"text-4xl font-bold text-white mb-6",children:"Ready to Transform Your APIs?"}),(0,cn.jsx)("p",{className:"text-xl text-blue-100 mb-8",children:"Join thousands of developers who are already using MCPify to power their AI assistants"}),(0,cn.jsxs)(_e,{to:"/convert",className:"inline-flex items-center space-x-2 bg-white text-blue-600 px-8 py-4 rounded-lg text-lg font-medium hover:bg-gray-100 transition-all shadow-lg",children:[(0,cn.jsx)(ln,{className:"w-5 h-5"}),(0,cn.jsx)("span",{children:"Get Started Free"}),(0,cn.jsx)(vn,{className:"w-5 h-5"})]})]})})]})};function bn(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(n=bn(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function xn(){for(var e,t,n=0,r="",a=arguments.length;n<a;n++)(e=arguments[n])&&(t=bn(e))&&(r&&(r+=" "),r+=t);return r}const wn=(e,t)=>{var n;if(0===e.length)return t.classGroupId;const r=e[0],a=t.nextPart.get(r),l=a?wn(e.slice(1),a):void 0;if(l)return l;if(0===t.validators.length)return;const o=e.join("-");return null===(n=t.validators.find((e=>{let{validator:t}=e;return t(o)})))||void 0===n?void 0:n.classGroupId},kn=/^\[(.+)\]$/,Sn=e=>{if(kn.test(e)){const t=kn.exec(e)[1],n=null===t||void 0===t?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},Nn=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return Pn(Object.entries(e.classGroups),n).forEach((e=>{let[n,a]=e;Cn(a,r,n,t)})),r},Cn=(e,t,n,r)=>{e.forEach((e=>{if("string"!==typeof e){if("function"===typeof e)return jn(e)?void Cn(e(r),t,n,r):void t.validators.push({validator:e,classGroupId:n});Object.entries(e).forEach((e=>{let[a,l]=e;Cn(l,En(t,a),n,r)}))}else{(""===e?t:En(t,e)).classGroupId=n}}))},En=(e,t)=>{let n=e;return t.split("-").forEach((e=>{n.nextPart.has(e)||n.nextPart.set(e,{nextPart:new Map,validators:[]}),n=n.nextPart.get(e)})),n},jn=e=>e.isThemeGetter,Pn=(e,t)=>t?e.map((e=>{let[n,r]=e;return[n,r.map((e=>"string"===typeof e?t+e:"object"===typeof e?Object.fromEntries(Object.entries(e).map((e=>{let[n,r]=e;return[t+n,r]}))):e))]})):e,_n=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const a=(a,l)=>{n.set(a,l),t++,t>e&&(t=0,r=n,n=new Map)};return{get(e){let t=n.get(e);return void 0!==t?t:void 0!==(t=r.get(e))?(a(e,t),t):void 0},set(e,t){n.has(e)?n.set(e,t):a(e,t)}}},zn=e=>{const{separator:t,experimentalParseClassName:n}=e,r=1===t.length,a=t[0],l=t.length,o=e=>{const n=[];let o,i=0,s=0;for(let d=0;d<e.length;d++){let u=e[d];if(0===i){if(u===a&&(r||e.slice(d,d+l)===t)){n.push(e.slice(s,d)),s=d+l;continue}if("/"===u){o=d;continue}}"["===u?i++:"]"===u&&i--}const u=0===n.length?e:e.substring(s),c=u.startsWith("!");return{modifiers:n,hasImportantModifier:c,baseClassName:c?u.substring(1):u,maybePostfixModifierPosition:o&&o>s?o-s:void 0}};return n?e=>n({className:e,parseClassName:o}):o},On=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach((e=>{"["===e[0]?(t.push(...n.sort(),e),n=[]):n.push(e)})),t.push(...n.sort()),t},Tn=e=>De({cache:_n(e.cacheSize),parseClassName:zn(e)},(e=>{const t=Nn(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:e=>{const n=e.split("-");return""===n[0]&&1!==n.length&&n.shift(),wn(n,t)||Sn(e)},getConflictingClassGroupIds:(e,t)=>{const a=n[e]||[];return t&&r[e]?[...a,...r[e]]:a}}})(e)),Mn=/\s+/;function Ln(){let e,t,n=0,r="";for(;n<arguments.length;)(e=arguments[n++])&&(t=Rn(e))&&(r&&(r+=" "),r+=t);return r}const Rn=e=>{if("string"===typeof e)return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=Rn(e[r]))&&(n&&(n+=" "),n+=t);return n};function In(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];let a,l,o,i=function(t){const r=n.reduce(((e,t)=>t(e)),e());return a=Tn(r),l=a.cache.get,o=a.cache.set,i=s,s(t)};function s(e){const t=l(e);if(t)return t;const n=((e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:a}=t,l=[],o=e.trim().split(Mn);let i="";for(let s=o.length-1;s>=0;s-=1){const e=o[s],{modifiers:t,hasImportantModifier:u,baseClassName:c,maybePostfixModifierPosition:d}=n(e);let f=Boolean(d),p=r(f?c.substring(0,d):c);if(!p){if(!f){i=e+(i.length>0?" "+i:i);continue}if(p=r(c),!p){i=e+(i.length>0?" "+i:i);continue}f=!1}const m=On(t).join(":"),h=u?m+"!":m,g=h+p;if(l.includes(g))continue;l.push(g);const v=a(p,f);for(let n=0;n<v.length;++n){const e=v[n];l.push(h+e)}i=e+(i.length>0?" "+i:i)}return i})(e,a);return o(e,n),n}return function(){return i(Ln.apply(null,arguments))}}const Dn=e=>{const t=t=>t[e]||[];return t.isThemeGetter=!0,t},Fn=/^\[(?:([a-z-]+):)?(.+)\]$/i,Un=/^\d+\/\d+$/,An=new Set(["px","full","screen"]),Bn=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Wn=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,$n=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Vn=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Hn=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Qn=e=>Gn(e)||An.has(e)||Un.test(e),qn=e=>ir(e,"length",sr),Gn=e=>Boolean(e)&&!Number.isNaN(Number(e)),Kn=e=>ir(e,"number",Gn),Yn=e=>Boolean(e)&&Number.isInteger(Number(e)),Xn=e=>e.endsWith("%")&&Gn(e.slice(0,-1)),Jn=e=>Fn.test(e),Zn=e=>Bn.test(e),er=new Set(["length","size","percentage"]),tr=e=>ir(e,er,ur),nr=e=>ir(e,"position",ur),rr=new Set(["image","url"]),ar=e=>ir(e,rr,dr),lr=e=>ir(e,"",cr),or=()=>!0,ir=(e,t,n)=>{const r=Fn.exec(e);return!!r&&(r[1]?"string"===typeof t?r[1]===t:t.has(r[1]):n(r[2]))},sr=e=>Wn.test(e)&&!$n.test(e),ur=()=>!1,cr=e=>Vn.test(e),dr=e=>Hn.test(e),fr=(Symbol.toStringTag,()=>{const e=Dn("colors"),t=Dn("spacing"),n=Dn("blur"),r=Dn("brightness"),a=Dn("borderColor"),l=Dn("borderRadius"),o=Dn("borderSpacing"),i=Dn("borderWidth"),s=Dn("contrast"),u=Dn("grayscale"),c=Dn("hueRotate"),d=Dn("invert"),f=Dn("gap"),p=Dn("gradientColorStops"),m=Dn("gradientColorStopPositions"),h=Dn("inset"),g=Dn("margin"),v=Dn("opacity"),y=Dn("padding"),b=Dn("saturate"),x=Dn("scale"),w=Dn("sepia"),k=Dn("skew"),S=Dn("space"),N=Dn("translate"),C=()=>["auto",Jn,t],E=()=>[Jn,t],j=()=>["",Qn,qn],P=()=>["auto",Gn,Jn],_=()=>["","0",Jn],z=()=>[Gn,Jn];return{cacheSize:500,separator:":",theme:{colors:[or],spacing:[Qn,qn],blur:["none","",Zn,Jn],brightness:z(),borderColor:[e],borderRadius:["none","","full",Zn,Jn],borderSpacing:E(),borderWidth:j(),contrast:z(),grayscale:_(),hueRotate:z(),invert:_(),gap:E(),gradientColorStops:[e],gradientColorStopPositions:[Xn,qn],inset:C(),margin:C(),opacity:z(),padding:E(),saturate:z(),scale:z(),sepia:_(),skew:z(),space:E(),translate:E()},classGroups:{aspect:[{aspect:["auto","square","video",Jn]}],container:["container"],columns:[{columns:[Zn]}],"break-after":[{"break-after":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-before":[{"break-before":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top",Jn]}],overflow:[{overflow:["auto","hidden","clip","visible","scroll"]}],"overflow-x":[{"overflow-x":["auto","hidden","clip","visible","scroll"]}],"overflow-y":[{"overflow-y":["auto","hidden","clip","visible","scroll"]}],overscroll:[{overscroll:["auto","contain","none"]}],"overscroll-x":[{"overscroll-x":["auto","contain","none"]}],"overscroll-y":[{"overscroll-y":["auto","contain","none"]}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[h]}],"inset-x":[{"inset-x":[h]}],"inset-y":[{"inset-y":[h]}],start:[{start:[h]}],end:[{end:[h]}],top:[{top:[h]}],right:[{right:[h]}],bottom:[{bottom:[h]}],left:[{left:[h]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Yn,Jn]}],basis:[{basis:C()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",Jn]}],grow:[{grow:_()}],shrink:[{shrink:_()}],order:[{order:["first","last","none",Yn,Jn]}],"grid-cols":[{"grid-cols":[or]}],"col-start-end":[{col:["auto",{span:["full",Yn,Jn]},Jn]}],"col-start":[{"col-start":P()}],"col-end":[{"col-end":P()}],"grid-rows":[{"grid-rows":[or]}],"row-start-end":[{row:["auto",{span:[Yn,Jn]},Jn]}],"row-start":[{"row-start":P()}],"row-end":[{"row-end":P()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",Jn]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",Jn]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal","start","end","center","between","around","evenly","stretch"]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal","start","end","center","between","around","evenly","stretch","baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":["start","end","center","between","around","evenly","stretch","baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[y]}],px:[{px:[y]}],py:[{py:[y]}],ps:[{ps:[y]}],pe:[{pe:[y]}],pt:[{pt:[y]}],pr:[{pr:[y]}],pb:[{pb:[y]}],pl:[{pl:[y]}],m:[{m:[g]}],mx:[{mx:[g]}],my:[{my:[g]}],ms:[{ms:[g]}],me:[{me:[g]}],mt:[{mt:[g]}],mr:[{mr:[g]}],mb:[{mb:[g]}],ml:[{ml:[g]}],"space-x":[{"space-x":[S]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[S]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",Jn,t]}],"min-w":[{"min-w":[Jn,t,"min","max","fit"]}],"max-w":[{"max-w":[Jn,t,"none","full","min","max","fit","prose",{screen:[Zn]},Zn]}],h:[{h:[Jn,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[Jn,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[Jn,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[Jn,t,"auto","min","max","fit"]}],"font-size":[{text:["base",Zn,qn]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Kn]}],"font-family":[{font:[or]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",Jn]}],"line-clamp":[{"line-clamp":["none",Gn,Kn]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Qn,Jn]}],"list-image":[{"list-image":["none",Jn]}],"list-style-type":[{list:["none","disc","decimal",Jn]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[v]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[v]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:["solid","dashed","dotted","double","none","wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Qn,qn]}],"underline-offset":[{"underline-offset":["auto",Qn,Jn]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:E()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Jn]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Jn]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[v]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top",nr]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",tr]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},ar]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[m]}],"gradient-via-pos":[{via:[m]}],"gradient-to-pos":[{to:[m]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[l]}],"rounded-s":[{"rounded-s":[l]}],"rounded-e":[{"rounded-e":[l]}],"rounded-t":[{"rounded-t":[l]}],"rounded-r":[{"rounded-r":[l]}],"rounded-b":[{"rounded-b":[l]}],"rounded-l":[{"rounded-l":[l]}],"rounded-ss":[{"rounded-ss":[l]}],"rounded-se":[{"rounded-se":[l]}],"rounded-ee":[{"rounded-ee":[l]}],"rounded-es":[{"rounded-es":[l]}],"rounded-tl":[{"rounded-tl":[l]}],"rounded-tr":[{"rounded-tr":[l]}],"rounded-br":[{"rounded-br":[l]}],"rounded-bl":[{"rounded-bl":[l]}],"border-w":[{border:[i]}],"border-w-x":[{"border-x":[i]}],"border-w-y":[{"border-y":[i]}],"border-w-s":[{"border-s":[i]}],"border-w-e":[{"border-e":[i]}],"border-w-t":[{"border-t":[i]}],"border-w-r":[{"border-r":[i]}],"border-w-b":[{"border-b":[i]}],"border-w-l":[{"border-l":[i]}],"border-opacity":[{"border-opacity":[v]}],"border-style":[{border:["solid","dashed","dotted","double","none","hidden"]}],"divide-x":[{"divide-x":[i]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[i]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[v]}],"divide-style":[{divide:["solid","dashed","dotted","double","none"]}],"border-color":[{border:[a]}],"border-color-x":[{"border-x":[a]}],"border-color-y":[{"border-y":[a]}],"border-color-s":[{"border-s":[a]}],"border-color-e":[{"border-e":[a]}],"border-color-t":[{"border-t":[a]}],"border-color-r":[{"border-r":[a]}],"border-color-b":[{"border-b":[a]}],"border-color-l":[{"border-l":[a]}],"divide-color":[{divide:[a]}],"outline-style":[{outline:["","solid","dashed","dotted","double","none"]}],"outline-offset":[{"outline-offset":[Qn,Jn]}],"outline-w":[{outline:[Qn,qn]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:j()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[v]}],"ring-offset-w":[{"ring-offset":[Qn,qn]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Zn,lr]}],"shadow-color":[{shadow:[or]}],opacity:[{opacity:[v]}],"mix-blend":[{"mix-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"]}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[s]}],"drop-shadow":[{"drop-shadow":["","none",Zn,Jn]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[d]}],saturate:[{saturate:[b]}],sepia:[{sepia:[w]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[s]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[v]}],"backdrop-saturate":[{"backdrop-saturate":[b]}],"backdrop-sepia":[{"backdrop-sepia":[w]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[o]}],"border-spacing-x":[{"border-spacing-x":[o]}],"border-spacing-y":[{"border-spacing-y":[o]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",Jn]}],duration:[{duration:z()}],ease:[{ease:["linear","in","out","in-out",Jn]}],delay:[{delay:z()}],animate:[{animate:["none","spin","ping","pulse","bounce",Jn]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[x]}],"scale-x":[{"scale-x":[x]}],"scale-y":[{"scale-y":[x]}],rotate:[{rotate:[Yn,Jn]}],"translate-x":[{"translate-x":[N]}],"translate-y":[{"translate-y":[N]}],"skew-x":[{"skew-x":[k]}],"skew-y":[{"skew-y":[k]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",Jn]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Jn]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":E()}],"scroll-mx":[{"scroll-mx":E()}],"scroll-my":[{"scroll-my":E()}],"scroll-ms":[{"scroll-ms":E()}],"scroll-me":[{"scroll-me":E()}],"scroll-mt":[{"scroll-mt":E()}],"scroll-mr":[{"scroll-mr":E()}],"scroll-mb":[{"scroll-mb":E()}],"scroll-ml":[{"scroll-ml":E()}],"scroll-p":[{"scroll-p":E()}],"scroll-px":[{"scroll-px":E()}],"scroll-py":[{"scroll-py":E()}],"scroll-ps":[{"scroll-ps":E()}],"scroll-pe":[{"scroll-pe":E()}],"scroll-pt":[{"scroll-pt":E()}],"scroll-pr":[{"scroll-pr":E()}],"scroll-pb":[{"scroll-pb":E()}],"scroll-pl":[{"scroll-pl":E()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Jn]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Qn,qn,Kn]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}}),pr=In(fr);const mr=["className","variant","size","loading","icon","children","disabled","onClick","type"],hr=e=>{let{className:t,variant:n="primary",size:r="md",loading:l=!1,icon:o,children:i,disabled:s,onClick:u,type:c="button"}=e,d=Xt(e,mr);const f=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return pr(xn(t))}("inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-indigo-500",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-indigo-500",destructive:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500"}[n],{sm:"h-8 px-3 text-sm",md:"h-10 px-4 text-sm",lg:"h-12 px-6 text-base"}[r],t);return a.createElement("button",De({className:f,disabled:s||l,onClick:u,type:c},d),l&&a.createElement("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},a.createElement("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),a.createElement("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})),!l&&o&&a.createElement("span",{className:"mr-2"},o),i)},gr=()=>{const[e,t]=(0,a.useState)(!1),[n,r]=(0,a.useState)(null),[l,o]=(0,a.useState)(""),[i,s]=(0,a.useState)(null),[u,c]=(0,a.useState)(null),[d,f]=(0,a.useState)(null),[p,m]=(0,a.useState)(!1);(0,a.useRef)(null);return(0,cn.jsx)("div",{className:"p-6",children:(0,cn.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,cn.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Generate MCP Server"}),(0,cn.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,cn.jsxs)("div",{className:"mt-6",children:[(0,cn.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Paste OpenAPI URL"}),(0,cn.jsx)("div",{className:"mt-1",children:(0,cn.jsx)("input",{type:"url",value:l,onChange:e=>{o(e.target.value),r(null),s(null)},className:"shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md",placeholder:"https://api.example.com/openapi.json"})})]}),i&&(0,cn.jsx)("div",{className:"mt-4 p-3 bg-red-50 border border-red-200 rounded-md",children:(0,cn.jsx)("p",{className:"text-sm text-red-700",children:i})}),u&&(0,cn.jsxs)("div",{className:"mt-4 p-3 bg-green-50 border border-green-200 rounded-md",children:[(0,cn.jsx)("p",{className:"text-sm text-green-700",children:u}),d&&(0,cn.jsx)("button",{onClick:()=>(async e=>{try{const t=e.startsWith("/api/")?"http://localhost:3000".concat(e):e,n=await fetch(t);if(!n.ok)throw new Error("Download failed: ".concat(n.status));let r="mcp-server.zip";const a=n.headers.get("Content-Disposition");if(a){const e=a.match(/filename="([^"]+)"/);e&&(r=e[1])}const l=await n.blob(),o=window.URL.createObjectURL(l),i=document.createElement("a");i.href=o,i.download=r,document.body.appendChild(i),i.click(),document.body.removeChild(i),window.URL.revokeObjectURL(o)}catch(i){console.error("Download error:",i),s("Download failed. Please try again.")}})(d),className:"mt-2 inline-block text-sm text-blue-600 hover:text-blue-800 underline bg-transparent border-none cursor-pointer",children:"Download MCP Server"})]}),(0,cn.jsx)("div",{className:"mt-6",children:(0,cn.jsx)(hr,{onClick:async()=>{t(!0),s(null),c(null),f(null);try{if(!l.trim())return s("Please enter an OpenAPI URL"),void t(!1);const e=await fetch("http://localhost:3000/api/convert",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({openapi:l.trim(),config:{name:"generated-mcp-server",version:"1.0.0"}})});if(!e.ok){const n=await e.json();return s(n.error||"Failed to generate MCP server"),void t(!1)}const n=await e.json();console.log("Response data:",n),console.log("Download URL:",n.downloadUrl),c("MCP server generated successfully!"),f(n.downloadUrl)}catch(e){s(e.message||"Conversion failed")}finally{t(!1)}},disabled:!l.trim(),loading:e,className:"w-full",children:e?"Generating MCP Server...":"Generate MCP Server"})})]})]})})},vr=en("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),yr=en("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]),br=en("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),xr=en("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]]),wr=e=>{let{feature:t,description:n,expectedDate:r="Q1 2025"}=e;const[l,o]=a.useState(""),[i,s]=a.useState(!1);return(0,cn.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50",children:(0,cn.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,cn.jsxs)(_e,{to:"/convert",className:"inline-flex items-center space-x-2 text-gray-600 hover:text-gray-900 mb-8 transition-colors",children:[(0,cn.jsx)(vr,{className:"w-4 h-4"}),(0,cn.jsx)("span",{children:"Back to Converter"})]}),(0,cn.jsxs)("div",{className:"text-center",children:[(0,cn.jsx)("div",{className:"flex justify-center mb-6",children:(0,cn.jsxs)("div",{className:"relative",children:[(0,cn.jsx)("div",{className:"w-24 h-24 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center",children:(0,cn.jsx)(on,{className:"w-12 h-12 text-white"})}),(0,cn.jsx)("div",{className:"absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center",children:(0,cn.jsx)(yr,{className:"w-4 h-4 text-yellow-800"})})]})}),(0,cn.jsxs)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:[t," is Coming Soon!"]}),(0,cn.jsx)("p",{className:"text-xl text-gray-600 mb-8 max-w-2xl mx-auto",children:n}),(0,cn.jsxs)("div",{className:"flex items-center justify-center space-x-2 mb-8",children:[(0,cn.jsx)(br,{className:"w-5 h-5 text-blue-600"}),(0,cn.jsxs)("span",{className:"text-lg font-medium text-blue-600",children:["Expected: ",r]})]}),(0,cn.jsxs)("div",{className:"grid md:grid-cols-3 gap-6 mb-12",children:[(0,cn.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200",children:[(0,cn.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4 mx-auto",children:(0,cn.jsx)(gn,{className:"w-6 h-6 text-blue-600"})}),(0,cn.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Powerful Features"}),(0,cn.jsx)("p",{className:"text-gray-600 text-sm",children:"Advanced capabilities designed for professional use"})]}),(0,cn.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200",children:[(0,cn.jsx)("div",{className:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4 mx-auto",children:(0,cn.jsx)(on,{className:"w-6 h-6 text-purple-600"})}),(0,cn.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Intuitive Design"}),(0,cn.jsx)("p",{className:"text-gray-600 text-sm",children:"User-friendly interface that makes complex tasks simple"})]}),(0,cn.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200",children:[(0,cn.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4 mx-auto",children:(0,cn.jsx)(yr,{className:"w-6 h-6 text-green-600"})}),(0,cn.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Premium Quality"}),(0,cn.jsx)("p",{className:"text-gray-600 text-sm",children:"Built with attention to detail and performance"})]})]}),(0,cn.jsxs)("div",{className:"bg-white rounded-xl p-8 shadow-lg border border-gray-200 max-w-md mx-auto",children:[(0,cn.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,cn.jsx)(xr,{className:"w-6 h-6 text-blue-600 mr-2"}),(0,cn.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Get Notified"})]}),i?(0,cn.jsxs)("div",{className:"text-center",children:[(0,cn.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,cn.jsx)(yr,{className:"w-6 h-6 text-green-600"})}),(0,cn.jsx)("p",{className:"text-green-600 font-medium",children:"Thanks! We'll notify you when it's ready."})]}):(0,cn.jsxs)("form",{onSubmit:e=>{e.preventDefault(),s(!0),o("")},className:"space-y-4",children:[(0,cn.jsxs)("p",{className:"text-gray-600 text-sm mb-4",children:["Be the first to know when ",t.toLowerCase()," launches"]}),(0,cn.jsxs)("div",{className:"flex space-x-2",children:[(0,cn.jsx)("input",{type:"email",value:l,onChange:e=>o(e.target.value),placeholder:"Enter your email",className:"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",required:!0}),(0,cn.jsx)("button",{type:"submit",className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all",children:"Notify Me"})]})]})]}),(0,cn.jsxs)("div",{className:"mt-12",children:[(0,cn.jsx)("p",{className:"text-gray-600 mb-4",children:"In the meantime, try our API to MCP converter"}),(0,cn.jsxs)(_e,{to:"/convert",className:"inline-flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all",children:[(0,cn.jsx)(gn,{className:"w-4 h-4"}),(0,cn.jsx)("span",{children:"Start Converting APIs"})]})]})]})]})})},kr=en("Check",[["polyline",{points:"20 6 9 17 4 12",key:"10jjfj"}]]),Sr=en("Headphones",[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]]),Nr=()=>(0,cn.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,cn.jsx)("section",{className:"bg-white py-16",children:(0,cn.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,cn.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-6",children:"Simple, Transparent Pricing"}),(0,cn.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Choose the plan that fits your needs. Start free and upgrade as you grow."})]})}),(0,cn.jsx)("section",{className:"py-16",children:(0,cn.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,cn.jsx)("div",{className:"grid md:grid-cols-3 gap-8",children:[{name:"Free",price:"$0",period:"forever",description:"Perfect for getting started",features:["5 API conversions per month","Basic MCP server generation","Community support","Standard templates","Public repository hosting"],limitations:["No custom branding","Limited to 10 endpoints per API","Basic error handling"],cta:"Get Started",ctaVariant:"secondary",popular:!1},{name:"Pro",price:"$29",period:"per month",description:"For professional developers",features:["Unlimited API conversions","Advanced MCP server generation","Priority support","Custom templates","Private repository hosting","Advanced error handling","Custom branding","Analytics dashboard","Team collaboration (up to 5 members)"],limitations:[],cta:"Start Pro Trial",ctaVariant:"primary",popular:!0,badge:"Most Popular"},{name:"Enterprise",price:"Custom",period:"contact us",description:"For large teams and organizations",features:["Everything in Pro","Unlimited team members","Dedicated support","Custom integrations","On-premise deployment","SLA guarantees","Advanced security features","Custom training","White-label solution"],limitations:[],cta:"Contact Sales",ctaVariant:"secondary",popular:!1}].map(((e,t)=>(0,cn.jsxs)("div",{className:"relative bg-white rounded-2xl shadow-lg border-2 transition-all hover:shadow-xl ".concat(e.popular?"border-blue-500 scale-105":"border-gray-200"),children:[e.popular&&(0,cn.jsx)("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:(0,cn.jsxs)("span",{className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-full text-sm font-medium flex items-center space-x-1",children:[(0,cn.jsx)(yr,{className:"w-4 h-4"}),(0,cn.jsx)("span",{children:e.badge})]})}),(0,cn.jsxs)("div",{className:"p-8",children:[(0,cn.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:e.name}),(0,cn.jsx)("p",{className:"text-gray-600 mb-6",children:e.description}),(0,cn.jsxs)("div",{className:"mb-6",children:[(0,cn.jsx)("span",{className:"text-4xl font-bold text-gray-900",children:e.price}),e.period&&(0,cn.jsxs)("span",{className:"text-gray-600 ml-2",children:["/",e.period]})]}),(0,cn.jsx)("button",{className:"w-full py-3 px-6 rounded-lg font-medium transition-all mb-8 ".concat("primary"===e.ctaVariant?"bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700":"bg-gray-100 text-gray-900 hover:bg-gray-200"),children:e.cta}),(0,cn.jsxs)("div",{className:"space-y-4",children:[(0,cn.jsxs)("h4",{className:"font-semibold text-gray-900 flex items-center",children:[(0,cn.jsx)(kr,{className:"w-5 h-5 text-green-500 mr-2"}),"What's included:"]}),(0,cn.jsx)("ul",{className:"space-y-3",children:e.features.map(((e,t)=>(0,cn.jsxs)("li",{className:"flex items-start",children:[(0,cn.jsx)(kr,{className:"w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0"}),(0,cn.jsx)("span",{className:"text-gray-600",children:e})]},t)))})]})]})]},t)))})})}),(0,cn.jsx)("section",{className:"py-16 bg-white",children:(0,cn.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,cn.jsxs)("div",{className:"text-center mb-12",children:[(0,cn.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Why Upgrade to Pro?"}),(0,cn.jsx)("p",{className:"text-xl text-gray-600",children:"Unlock powerful features for professional development"})]}),(0,cn.jsxs)("div",{className:"grid md:grid-cols-3 gap-8",children:[(0,cn.jsxs)("div",{className:"text-center",children:[(0,cn.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,cn.jsx)(gn,{className:"w-8 h-8 text-blue-600"})}),(0,cn.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Unlimited Conversions"}),(0,cn.jsx)("p",{className:"text-gray-600",children:"Convert as many APIs as you need without any monthly limits"})]}),(0,cn.jsxs)("div",{className:"text-center",children:[(0,cn.jsx)("div",{className:"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,cn.jsx)(mn,{className:"w-8 h-8 text-purple-600"})}),(0,cn.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Advanced Security"}),(0,cn.jsx)("p",{className:"text-gray-600",children:"Enhanced security features and private repository hosting"})]}),(0,cn.jsxs)("div",{className:"text-center",children:[(0,cn.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,cn.jsx)(Sr,{className:"w-8 h-8 text-green-600"})}),(0,cn.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Priority Support"}),(0,cn.jsx)("p",{className:"text-gray-600",children:"Get help when you need it with our priority support team"})]})]})]})}),(0,cn.jsx)("section",{className:"py-16 bg-gray-50",children:(0,cn.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,cn.jsx)("div",{className:"text-center mb-12",children:(0,cn.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Frequently Asked Questions"})}),(0,cn.jsx)("div",{className:"space-y-6",children:[{question:"What is MCP?",answer:"MCP (Model Context Protocol) is a standard that allows AI assistants to interact with external tools and APIs. Our service converts your APIs into MCP-compatible servers."},{question:"Can I cancel anytime?",answer:"Yes, you can cancel your subscription at any time. Your access will continue until the end of your current billing period."},{question:"Do you offer refunds?",answer:"We offer a 30-day money-back guarantee for all paid plans. If you're not satisfied, we'll refund your payment."},{question:"What APIs are supported?",answer:"We support any REST API with OpenAPI 3.0+ specification. This includes most modern APIs from popular services."}].map(((e,t)=>(0,cn.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm",children:[(0,cn.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:e.question}),(0,cn.jsx)("p",{className:"text-gray-600",children:e.answer})]},t)))})]})}),(0,cn.jsx)("section",{className:"py-16 bg-gradient-to-r from-blue-600 to-purple-600",children:(0,cn.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,cn.jsx)("h2",{className:"text-3xl font-bold text-white mb-6",children:"Ready to Get Started?"}),(0,cn.jsx)("p",{className:"text-xl text-blue-100 mb-8",children:"Join thousands of developers already using MCPify"}),(0,cn.jsxs)(_e,{to:"/convert",className:"inline-flex items-center space-x-2 bg-white text-blue-600 px-8 py-4 rounded-lg text-lg font-medium hover:bg-gray-100 transition-all",children:[(0,cn.jsx)(ln,{className:"w-5 h-5"}),(0,cn.jsx)("span",{children:"Start Free Trial"}),(0,cn.jsx)(vn,{className:"w-5 h-5"})]})]})})]});const Cr=function(){return(0,cn.jsx)(Ee,{children:(0,cn.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,cn.jsx)(dn,{}),(0,cn.jsx)("main",{className:"flex-1",children:(0,cn.jsxs)(xe,{children:[(0,cn.jsx)(ye,{path:"/",element:(0,cn.jsx)(yn,{})}),(0,cn.jsx)(ye,{path:"/convert",element:(0,cn.jsx)(gr,{})}),(0,cn.jsx)(ye,{path:"/pricing",element:(0,cn.jsx)(Nr,{})}),(0,cn.jsx)(ye,{path:"/chat",element:(0,cn.jsx)(wr,{feature:"AI Chat Interface",description:"Chat directly with your APIs using natural language. Ask questions, get data, and perform actions through an intuitive chat interface powered by your MCP servers.",expectedDate:"Q1 2025"})}),(0,cn.jsx)(ye,{path:"/tools",element:(0,cn.jsx)(wr,{feature:"MCP Tools Manager",description:"Manage, monitor, and debug your MCP servers with our comprehensive tools dashboard. View logs, test endpoints, and optimize performance.",expectedDate:"Q1 2025"})}),(0,cn.jsx)(ye,{path:"/playground",element:(0,cn.jsx)(wr,{feature:"API Playground",description:"Test and experiment with your APIs before converting them to MCP servers. Interactive documentation and testing environment.",expectedDate:"Q2 2025"})}),(0,cn.jsx)(ye,{path:"/profile",element:(0,cn.jsx)(wr,{feature:"User Profile",description:"Manage your account, view conversion history, and customize your MCPify experience.",expectedDate:"Q1 2025"})}),(0,cn.jsx)(ye,{path:"/settings",element:(0,cn.jsx)(wr,{feature:"Settings",description:"Configure your preferences, manage integrations, and customize your workflow.",expectedDate:"Q1 2025"})}),(0,cn.jsx)(ye,{path:"*",element:(0,cn.jsx)(ve,{to:"/",replace:!0})})]})}),(0,cn.jsx)(Yt,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#fff",color:"#374151",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"}}})]})})};o.createRoot(document.getElementById("root")).render((0,cn.jsx)(a.StrictMode,{children:(0,cn.jsx)(Cr,{})}))})();
//# sourceMappingURL=main.5da5947f.js.map