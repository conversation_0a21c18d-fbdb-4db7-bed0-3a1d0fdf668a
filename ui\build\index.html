<!doctype html><html lang="en"><head><meta charset="utf-8"/><link rel="icon" href="/favicon.ico"/><meta name="viewport" content="width=device-width,initial-scale=1"/><meta name="theme-color" content="#3b82f6"/><meta name="description" content="OpenAPI to MCP Converter - Convert OpenAPI specifications to MCP servers with chat interface"/><link rel="apple-touch-icon" href="/logo192.png"/><link rel="manifest" href="/manifest.json"/><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><meta property="og:title" content="OpenAPI to MCP Converter"><meta property="og:description" content="Convert OpenAPI specifications to MCP servers with interactive chat interface"><meta property="og:type" content="website"><meta property="og:url" content="https://openapi-to-mcp.vercel.app"><meta property="og:image" content="/og-image.png"><meta name="twitter:card" content="summary_large_image"><meta name="twitter:title" content="OpenAPI to MCP Converter"><meta name="twitter:description" content="Convert OpenAPI specifications to MCP servers with interactive chat interface"><meta name="twitter:image" content="/og-image.png"><link rel="icon" type="image/svg+xml" href="/favicon.svg"><link rel="icon" type="image/png" href="/favicon.png"><title>OpenAPI to MCP Converter</title><style>html{font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif}body{margin:0;background-color:#f9fafb;color:#111827}#root{min-height:100vh}.loading-container{display:flex;align-items:center;justify-content:center;min-height:100vh;flex-direction:column;gap:1rem}.loading-spinner{width:40px;height:40px;border:4px solid #e5e7eb;border-top:4px solid #3b82f6;border-radius:50%;animation:spin 1s linear infinite}@keyframes spin{0%{transform:rotate(0)}100%{transform:rotate(360deg)}}.loading-text{color:#6b7280;font-size:14px}</style><script defer="defer" src="/static/js/main.5da5947f.js"></script><link href="/static/css/main.505ef590.css" rel="stylesheet"></head><body><noscript><div style="padding:2rem;text-align:center;background:#fef2f2;color:#991b1b;border:1px solid #fecaca"><h2>JavaScript Required</h2><p>You need to enable JavaScript to run this application.</p><p>This is a React-based web application that requires JavaScript to function properly.</p></div></noscript><div id="root"><div class="loading-container"><div class="loading-spinner"></div><div class="loading-text">Loading OpenAPI to MCP Converter...</div></div></div><script>"serviceWorker"in navigator&&window.addEventListener("load",(function(){navigator.serviceWorker.register("/sw.js").then((function(e){console.log("SW registered: ",e)})).catch((function(e){console.log("SW registration failed: ",e)}))}))</script></body></html>