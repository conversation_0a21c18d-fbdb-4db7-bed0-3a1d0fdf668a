#!/usr/bin/env node

/**
 * MCP Bridge for Cline
 * Converts stdio MCP protocol to HTTP calls
 */

const http = require('http');

// Configuration
const MCP_SERVER_URL = process.env.MCP_SERVER_URL || 'http://localhost:8000/mcp';
const TIMEOUT = parseInt(process.env.TIMEOUT) || 30000;

// Buffer for incoming data
let inputBuffer = '';

// Handle stdin data
process.stdin.on('data', async (chunk) => {
  inputBuffer += chunk.toString();
  
  // Process complete JSON lines
  const lines = inputBuffer.split('\n');
  inputBuffer = lines.pop() || '';
  
  for (const line of lines) {
    if (line.trim()) {
      await handleMCPRequest(line.trim());
    }
  }
});

// Handle end of input
process.stdin.on('end', () => {
  process.exit(0);
});

// Handle MCP request
async function handleMCPRequest(jsonLine) {
  try {
    const request = JSON.parse(jsonLine);
    
    // Make HTTP request to MCP server
    const response = await makeHttpRequest(MCP_SERVER_URL, request);
    
    // Send response to stdout
    process.stdout.write(JSON.stringify(response) + '\n');
    
  } catch (error) {
    // Send error response
    const errorResponse = {
      jsonrpc: '2.0',
      id: null,
      error: {
        code: -32603,
        message: `Bridge error: ${error.message}`
      }
    };
    process.stdout.write(JSON.stringify(errorResponse) + '\n');
  }
}

// Make HTTP request
function makeHttpRequest(url, data) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(data);
    
    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      },
      timeout: TIMEOUT
    };
    
    const req = http.request(url, options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(responseData);
          resolve(response);
        } catch (error) {
          reject(new Error(`Invalid JSON response: ${responseData}`));
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.write(postData);
    req.end();
  });
}

// Handle process signals
process.on('SIGINT', () => {
  process.exit(0);
});

process.on('SIGTERM', () => {
  process.exit(0);
});

console.error('🔌 MCP Bridge started, connecting to:', MCP_SERVER_URL);
