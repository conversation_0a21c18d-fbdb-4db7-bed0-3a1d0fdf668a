const express = require('express');
const path = require('path');

console.log('🚀 Starting test server...');

const app = express();
const PORT = process.env.PORT || 3001;

console.log('📦 Express app created');

// Basic middleware
app.use(express.json());

// Health check
app.get('/api/health', (req, res) => {
  console.log('🔧 Health check requested');
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Serve UI if it exists
const uiBuildPath = path.join(__dirname, 'ui/build');
console.log('🔍 Checking UI build path:', uiBuildPath);

try {
  const fs = require('fs');
  if (fs.existsSync(uiBuildPath)) {
    app.use(express.static(uiBuildPath));
    console.log('✅ Serving UI from:', uiBuildPath);
  } else {
    console.log('⚠️ UI build directory not found');
  }
} catch (error) {
  console.log('⚠️ Error setting up static files:', error.message);
}

// Fallback route
app.get('*', (req, res) => {
  if (!req.path.startsWith('/api/')) {
    const indexPath = path.join(uiBuildPath, 'index.html');
    if (require('fs').existsSync(indexPath)) {
      res.sendFile(indexPath);
    } else {
      res.send(`
        <html>
          <head><title>MCPify</title></head>
          <body>
            <h1>MCPify Server</h1>
            <p>Server is running but UI build not found.</p>
            <p><a href="/api/health">Health Check</a></p>
          </body>
        </html>
      `);
    }
  } else {
    res.status(404).json({ error: 'API endpoint not found' });
  }
});

console.log('🔗 Routes configured');

// Start server
console.log('🚀 Starting server on port', PORT);
const server = app.listen(PORT, () => {
  console.log(`🚀 Test server running on port ${PORT}`);
  console.log(`🌐 Web Interface: http://localhost:${PORT}/`);
  console.log(`🔧 Health Check: http://localhost:${PORT}/api/health`);
});

console.log('✅ Server setup complete');
