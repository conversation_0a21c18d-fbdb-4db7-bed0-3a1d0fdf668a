/**
 * API server entry point
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import * as path from 'path';
// axios removed - no longer needed for LLM calls
import { convertRoutes } from './routes/convert';
import { downloadRoutes } from './routes/download';
import { errorHandler } from './middleware/errorHandler';
import instantMcpRoutes from './routes/instantMcp';
import { serversRoutes } from './routes/servers';
import ServerManager from './services/serverManager';

console.log('🚀 Starting MCPify server...');

const app = express();
const PORT = process.env.PORT || 3000;

console.log('📦 Express app created');
console.log('🔧 Port:', PORT);

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.CORS_ORIGIN || '*',
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Serve static files from UI build (only if directory exists)
const uiBuildPath = path.join(__dirname, '../ui/build');
try {
  if (require('fs').existsSync(uiBuildPath)) {
    app.use(express.static(uiBuildPath));
    console.log('✅ Serving UI from:', uiBuildPath);
  } else {
    console.log('⚠️ UI build directory not found:', uiBuildPath);
  }
} catch (error) {
  console.log('⚠️ Error setting up static files:', error instanceof Error ? error.message : String(error));
}

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    uptime: process.uptime()
  });
});

// API routes
console.log('🔗 Setting up routes...');
try {
  app.use('/api/convert', convertRoutes);
  console.log('✅ Convert routes loaded');
  app.use('/api/download', downloadRoutes);
  console.log('✅ Download routes loaded');
  app.use('/api/instant-mcp', instantMcpRoutes);
  console.log('✅ Instant MCP routes loaded');
  app.use('/api/servers', serversRoutes);
  console.log('✅ Servers routes loaded');
} catch (error) {
  console.error('❌ Error setting up routes:', error);
}

// No chat endpoint - this is a pure MCP server generator

// API info endpoint
app.get('/api', (req, res) => {
  res.json({
    name: 'MCPify - OpenAPI to MCP Server Generator',
    version: process.env.npm_package_version || '1.0.0',
    description: 'Convert OpenAPI specifications to MCP servers',
    endpoints: {
      '/api/health': 'GET - Health check',
      '/api/convert': 'POST - Convert OpenAPI to MCP',
      '/api/download/:bundleId': 'GET - Download generated bundle'
    },
    documentation: 'https://mcpify.com/docs'
  });
});

// Error handling middleware (must be last)
app.use(errorHandler);

// Serve React app for all non-API routes (SPA fallback)
app.get('*', (req, res) => {
  if (!req.path.startsWith('/api/')) {
    res.sendFile(path.join(__dirname, '../ui/build/index.html'));
  } else {
    res.status(404).json({
      error: 'Not Found',
      message: `Route ${req.originalUrl} not found`,
      availableEndpoints: [
        'GET /api',
        'GET /api/health',
        'POST /api/convert',
        'GET /api/download/:bundleId'
      ]
    });
  }
});

// Start server
const server = app.listen(PORT, () => {
  console.log(`🚀 MCPify Server running on port ${PORT}`);
  console.log(`🌐 Web Interface: http://localhost:${PORT}/`);
  console.log(`📖 API Documentation: http://localhost:${PORT}/api`);
  console.log(`🔧 Health Check: http://localhost:${PORT}/api/health`);
  console.log(`⚡ Convert APIs to MCP servers at: http://localhost:${PORT}/convert`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

export default app;
