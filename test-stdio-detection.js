#!/usr/bin/env node

/**
 * Quick test to verify stdio detection logic
 */

console.log('Testing stdio detection...');
console.log('process.stdin.isTTY:', process.stdin.isTTY);
console.log('process.stdout.isTTY:', process.stdout.isTTY);
console.log('process.stderr.isTTY:', process.stderr.isTTY);

const isStdioMode = process.stdin.isTTY === false;
console.log('isStdioMode:', isStdioMode);

if (isStdioMode) {
  console.error('🔌 Would start in stdio mode');
  
  // Simulate MCP response
  process.stdin.on('data', (chunk) => {
    const input = chunk.toString().trim();
    console.error('Received input:', input);
    
    try {
      const request = JSON.parse(input);
      const response = {
        jsonrpc: '2.0',
        id: request.id,
        result: {
          protocolVersion: '2024-11-05',
          capabilities: { tools: {} },
          serverInfo: { name: 'test-server', version: '1.0.0' }
        }
      };
      process.stdout.write(JSON.stringify(response) + '\n');
    } catch (error) {
      console.error('Parse error:', error.message);
    }
  });
  
  process.stdin.on('end', () => {
    process.exit(0);
  });
} else {
  console.log('🌐 Would start in HTTP mode');
  process.exit(0);
}
