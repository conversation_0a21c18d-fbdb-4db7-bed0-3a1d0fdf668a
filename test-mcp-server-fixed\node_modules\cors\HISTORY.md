2.8.5 / 2018-11-04
==================

  * Fix setting `maxAge` option to `0`

2.8.4 / 2017-07-12
==================

  * Work-around Safari bug in default pre-flight response

2.8.3 / 2017-03-29
==================

  * Fix error when options delegate missing `methods` option

2.8.2 / 2017-03-28
==================

  * Fix error when frozen options are passed
  * Send "Vary: Origin" when using regular expressions
  * Send "Vary: Access-Control-Request-Headers" when dynamic `allowedHeaders`

2.8.1 / 2016-09-08
==================

This release only changed documentation.

2.8.0 / 2016-08-23
==================

  * Add `optionsSuccessStatus` option

2.7.2 / 2016-08-23
==================

  * Fix error when Node.js running in strict mode

2.7.1 / 2015-05-28
==================

  * Move module into expressjs organization

2.7.0 / 2015-05-28
==================

  * Allow array of matching condition as `origin` option
  * Allow regular expression as `origin` option

2.6.1 / 2015-05-28
==================

  * Update `license` in package.json

2.6.0 / 2015-04-27
==================

  * Add `preflightContinue` option
  * Fix "Vary: Origin" header added for "*"
