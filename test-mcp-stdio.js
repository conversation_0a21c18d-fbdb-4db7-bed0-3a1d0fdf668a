#!/usr/bin/env node

/**
 * Test script to verify MCP server stdio communication
 * This simulates how <PERSON><PERSON> communicates with the MCP server
 */

const { spawn } = require('child_process');
const path = require('path');

// ANSI colors for better output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(color, message) {
  console.log(`${color}${message}${colors.reset}`);
}

async function testMCPServer(serverPath) {
  return new Promise((resolve, reject) => {
    log(colors.blue, `🧪 Testing MCP server: ${serverPath}`);
    
    // Spawn the server process
    const server = spawn('node', [serverPath], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let responseBuffer = '';
    let errorBuffer = '';
    let testResults = {
      started: false,
      initialize: false,
      toolsList: false,
      toolsCall: false,
      errors: []
    };

    // Handle stdout (JSON responses)
    server.stdout.on('data', (data) => {
      responseBuffer += data.toString();
      
      // Process complete JSON lines
      const lines = responseBuffer.split('\n');
      responseBuffer = lines.pop() || '';
      
      for (const line of lines) {
        if (line.trim()) {
          try {
            const response = JSON.parse(line);
            handleResponse(response, testResults);
          } catch (error) {
            log(colors.red, `❌ Invalid JSON response: ${line}`);
            testResults.errors.push(`Invalid JSON: ${line}`);
          }
        }
      }
    });

    // Handle stderr (server logs)
    server.stderr.on('data', (data) => {
      errorBuffer += data.toString();
      const message = data.toString().trim();
      
      if (message.includes('MCP Server starting in stdio mode')) {
        testResults.started = true;
        log(colors.green, '✅ Server started in stdio mode');
      } else if (message.includes('running on port')) {
        log(colors.red, '❌ Server started in HTTP mode (should be stdio)');
        testResults.errors.push('Server started in HTTP mode instead of stdio');
      }
    });

    // Handle process exit
    server.on('close', (code) => {
      if (code !== 0) {
        testResults.errors.push(`Server exited with code ${code}`);
      }
      resolve(testResults);
    });

    // Handle process errors
    server.on('error', (error) => {
      log(colors.red, `❌ Server process error: ${error.message}`);
      testResults.errors.push(`Process error: ${error.message}`);
      resolve(testResults);
    });

    // Test sequence
    setTimeout(() => {
      // Test 1: Initialize
      log(colors.yellow, '📤 Sending initialize request...');
      const initRequest = {
        jsonrpc: '2.0',
        id: 1,
        method: 'initialize',
        params: {
          protocolVersion: '2024-11-05',
          capabilities: {},
          clientInfo: { name: 'test-client', version: '1.0.0' }
        }
      };
      server.stdin.write(JSON.stringify(initRequest) + '\n');
    }, 100);

    setTimeout(() => {
      // Test 2: List tools
      log(colors.yellow, '📤 Sending tools/list request...');
      const toolsRequest = {
        jsonrpc: '2.0',
        id: 2,
        method: 'tools/list',
        params: {}
      };
      server.stdin.write(JSON.stringify(toolsRequest) + '\n');
    }, 500);

    setTimeout(() => {
      // Test 3: Call a tool (if available)
      log(colors.yellow, '📤 Sending tools/call request...');
      const callRequest = {
        jsonrpc: '2.0',
        id: 3,
        method: 'tools/call',
        params: {
          name: 'getPetById',
          arguments: { petId: '1' }
        }
      };
      server.stdin.write(JSON.stringify(callRequest) + '\n');
    }, 1000);

    // Cleanup after tests
    setTimeout(() => {
      server.kill('SIGTERM');
    }, 2000);
  });
}

function handleResponse(response, testResults) {
  const { id, result, error } = response;
  
  if (error) {
    log(colors.red, `❌ Error response (id: ${id}): ${error.message}`);
    testResults.errors.push(`Error ${error.code}: ${error.message}`);
    return;
  }

  switch (id) {
    case 1: // Initialize response
      if (result && result.protocolVersion) {
        testResults.initialize = true;
        log(colors.green, `✅ Initialize successful (protocol: ${result.protocolVersion})`);
        if (result.serverInfo) {
          log(colors.blue, `   Server: ${result.serverInfo.name} v${result.serverInfo.version}`);
        }
      } else {
        log(colors.red, '❌ Initialize failed - invalid response');
        testResults.errors.push('Initialize response missing required fields');
      }
      break;

    case 2: // Tools list response
      if (result && result.tools && Array.isArray(result.tools)) {
        testResults.toolsList = true;
        log(colors.green, `✅ Tools list successful (${result.tools.length} tools)`);
        result.tools.slice(0, 3).forEach(tool => {
          log(colors.blue, `   - ${tool.name}: ${tool.description}`);
        });
        if (result.tools.length > 3) {
          log(colors.blue, `   ... and ${result.tools.length - 3} more tools`);
        }
      } else {
        log(colors.red, '❌ Tools list failed - invalid response');
        testResults.errors.push('Tools list response missing or invalid');
      }
      break;

    case 3: // Tool call response
      if (result) {
        testResults.toolsCall = true;
        log(colors.green, '✅ Tool call successful');
        if (result.content && result.content[0]) {
          const content = result.content[0].text;
          try {
            const parsed = JSON.parse(content);
            log(colors.blue, `   Response: ${JSON.stringify(parsed).substring(0, 100)}...`);
          } catch {
            log(colors.blue, `   Response: ${content.substring(0, 100)}...`);
          }
        }
      } else {
        log(colors.red, '❌ Tool call failed - no result');
        testResults.errors.push('Tool call response missing result');
      }
      break;

    default:
      log(colors.yellow, `📥 Unexpected response (id: ${id})`);
  }
}

function printSummary(results) {
  log(colors.bold, '\n📊 Test Summary:');
  log(results.started ? colors.green : colors.red, `   Server startup: ${results.started ? 'PASS' : 'FAIL'}`);
  log(results.initialize ? colors.green : colors.red, `   Initialize: ${results.initialize ? 'PASS' : 'FAIL'}`);
  log(results.toolsList ? colors.green : colors.red, `   Tools list: ${results.toolsList ? 'PASS' : 'FAIL'}`);
  log(results.toolsCall ? colors.green : colors.red, `   Tool call: ${results.toolsCall ? 'PASS' : 'FAIL'}`);
  
  const passed = [results.started, results.initialize, results.toolsList, results.toolsCall].filter(Boolean).length;
  const total = 4;
  
  if (results.errors.length > 0) {
    log(colors.red, '\n❌ Errors:');
    results.errors.forEach(error => log(colors.red, `   - ${error}`));
  }
  
  log(colors.bold, `\n🎯 Overall: ${passed}/${total} tests passed`);
  
  if (passed === total && results.errors.length === 0) {
    log(colors.green, '🎉 All tests passed! MCP server is working correctly.');
    return true;
  } else {
    log(colors.red, '💥 Some tests failed. Check the errors above.');
    return false;
  }
}

// Main execution
async function main() {
  const serverPath = process.argv[2];
  
  if (!serverPath) {
    log(colors.red, '❌ Usage: node test-mcp-stdio.js <path-to-server.js>');
    log(colors.yellow, '   Example: node test-mcp-stdio.js ./dist/server.js');
    process.exit(1);
  }

  if (!require('fs').existsSync(serverPath)) {
    log(colors.red, `❌ Server file not found: ${serverPath}`);
    process.exit(1);
  }

  try {
    const results = await testMCPServer(serverPath);
    const success = printSummary(results);
    process.exit(success ? 0 : 1);
  } catch (error) {
    log(colors.red, `❌ Test failed: ${error.message}`);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { testMCPServer };
