#!/usr/bin/env node

/**
 * Simple stdio test for MCP server
 */

const { spawn } = require('child_process');

async function testStdio() {
  console.log('🧪 Testing MCP Stdio Communication...\n');

  // Create a simple test server that echoes back JSON-RPC
  const testServerCode = `
const readline = require('readline');

// Set up stdio mode detection
const isStdioMode = (
  process.stdin.isTTY === false ||
  process.argv.includes('--stdio') ||
  process.env.MCP_STDIO_MODE === 'true' ||
  !process.stdout.isTTY
);

console.error('🔌 Test MCP Server starting in stdio mode');
console.error('🔧 Detection: stdin.isTTY=' + process.stdin.isTTY + ', stdout.isTTY=' + process.stdout.isTTY);

if (isStdioMode) {
  let buffer = '';
  
  process.stdin.setEncoding('utf8');
  
  process.stdin.on('data', async (chunk) => {
    try {
      buffer += chunk.toString();
      
      const lines = buffer.split(/\\r?\\n/);
      buffer = lines.pop() || '';
      
      for (const line of lines) {
        const trimmedLine = line.trim();
        if (trimmedLine) {
          console.error('📥 Received: ' + trimmedLine.substring(0, 100) + '...');
          
          try {
            const request = JSON.parse(trimmedLine);
            
            let response;
            if (request.method === 'initialize') {
              response = {
                jsonrpc: '2.0',
                id: request.id,
                result: {
                  protocolVersion: '2024-11-05',
                  capabilities: { tools: {} },
                  serverInfo: { name: 'test-server', version: '1.0.0' }
                }
              };
            } else if (request.method === 'tools/list') {
              response = {
                jsonrpc: '2.0',
                id: request.id,
                result: {
                  tools: [
                    { name: 'test-tool', description: 'A test tool' }
                  ]
                }
              };
            } else if (request.method === 'ping') {
              response = {
                jsonrpc: '2.0',
                id: request.id,
                result: {}
              };
            } else {
              response = {
                jsonrpc: '2.0',
                id: request.id,
                error: { code: -32601, message: 'Method not found' }
              };
            }
            
            const responseStr = JSON.stringify(response);
            console.error('📤 Sending: ' + responseStr.substring(0, 100) + '...');
            process.stdout.write(responseStr + '\\n');
            
          } catch (parseError) {
            const errorMessage = parseError instanceof Error ? parseError.message : String(parseError);
            console.error('❌ Parse error: ' + errorMessage);
            const errorResponse = {
              jsonrpc: '2.0',
              error: {
                code: -32700,
                message: 'Parse error',
                data: errorMessage
              }
            };
            process.stdout.write(JSON.stringify(errorResponse) + '\\n');
          }
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ Stdin error: ' + errorMessage);
    }
  });
  
  process.stdin.on('end', () => {
    console.error('📝 Stdin ended, exiting...');
    process.exit(0);
  });
  
} else {
  console.log('Not in stdio mode');
  process.exit(1);
}
`;

  // Write test server to file
  require('fs').writeFileSync('temp-test-server.js', testServerCode);

  // Spawn the test server
  const server = spawn('node', ['temp-test-server.js'], {
    stdio: ['pipe', 'pipe', 'pipe'],
    env: { 
      ...process.env, 
      MCP_STDIO_MODE: 'true'
    }
  });

  let responses = [];

  // Handle server stdout
  server.stdout.on('data', (data) => {
    const lines = data.toString().split('\n');
    for (const line of lines) {
      if (line.trim()) {
        try {
          const response = JSON.parse(line.trim());
          console.log('✅ Valid JSON Response:', JSON.stringify(response, null, 2));
          responses.push(response);
        } catch (e) {
          console.log('📤 Raw Output:', line.trim());
        }
      }
    }
  });

  // Handle server stderr
  server.stderr.on('data', (data) => {
    console.log('🔍 Server Log:', data.toString().trim());
  });

  // Wait for server to start
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Test messages
  const tests = [
    { jsonrpc: '2.0', id: 1, method: 'initialize', params: {} },
    { jsonrpc: '2.0', id: 2, method: 'tools/list', params: {} },
    { jsonrpc: '2.0', id: 3, method: 'ping', params: {} },
    'invalid-json'  // Test error handling
  ];

  for (let i = 0; i < tests.length; i++) {
    const test = tests[i];
    console.log(`\n🧪 Test ${i + 1}:`);

    if (typeof test === 'string') {
      console.log('📥 Sending invalid JSON:', test);
      server.stdin.write(test + '\n');
    } else {
      console.log('📥 Sending:', JSON.stringify(test));
      server.stdin.write(JSON.stringify(test) + '\n');
    }

    await new Promise(resolve => setTimeout(resolve, 500));
  }

  // Close server
  console.log('\\n🛑 Closing server...');
  server.stdin.end();

  await new Promise((resolve) => {
    server.on('close', (code) => {
      console.log(`Server exited with code: ${code}`);
      resolve();
    });
    setTimeout(() => {
      server.kill();
      resolve();
    }, 2000);
  });

  // Clean up
  require('fs').unlinkSync('temp-test-server.js');

  // Results
  console.log(`\n📊 Results: Received ${responses.length} valid JSON responses`);
  
  if (responses.length >= 3) {
    console.log('🎉 Stdio communication is working correctly!');
    return true;
  } else {
    console.log('❌ Stdio communication failed');
    return false;
  }
}

testStdio().catch(console.error);
