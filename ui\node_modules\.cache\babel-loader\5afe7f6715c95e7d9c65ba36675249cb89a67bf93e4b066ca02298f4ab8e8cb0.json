{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Subtitles = createLucideIcon(\"Subtitles\", [[\"path\", {\n  d: \"M7 13h4\",\n  key: \"1m1xj0\"\n}], [\"path\", {\n  d: \"M15 13h2\",\n  key: \"vgjay3\"\n}], [\"path\", {\n  d: \"M7 9h2\",\n  key: \"1q072n\"\n}], [\"path\", {\n  d: \"M13 9h4\",\n  key: \"o7fxw0\"\n}], [\"path\", {\n  d: \"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10Z\",\n  key: \"12yqn6\"\n}]]);\nexport { Subtitles as default };", "map": {"version": 3, "names": ["Subtitles", "createLucideIcon", "d", "key"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\subtitles.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Subtitles\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNyAxM2g0IiAvPgogIDxwYXRoIGQ9Ik0xNSAxM2gyIiAvPgogIDxwYXRoIGQ9Ik03IDloMiIgLz4KICA8cGF0aCBkPSJNMTMgOWg0IiAvPgogIDxwYXRoIGQ9Ik0yMSAxNWEyIDIgMCAwIDEtMiAySDdsLTQgNFY1YTIgMiAwIDAgMSAyLTJoMTRhMiAyIDAgMCAxIDIgMnYxMFoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/subtitles\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Subtitles = createLucideIcon('Subtitles', [\n  ['path', { d: 'M7 13h4', key: '1m1xj0' }],\n  ['path', { d: 'M15 13h2', key: 'vgjay3' }],\n  ['path', { d: 'M7 9h2', key: '1q072n' }],\n  ['path', { d: 'M13 9h4', key: 'o7fxw0' }],\n  [\n    'path',\n    {\n      d: 'M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10Z',\n      key: '12yqn6',\n    },\n  ],\n]);\n\nexport default Subtitles;\n"], "mappings": ";;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,QAAU;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}