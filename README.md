# 🚀 MCPify - Professional OpenAPI to MCP Converter

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Railway Deploy](https://img.shields.io/badge/Deploy-Railway-blueviolet)](https://railway.app)
[![Production Ready](https://img.shields.io/badge/Status-Production%20Ready-brightgreen)](https://github.com/yourusername/mcpify)

Transform any OpenAPI specification into a fully functional MCP (Model Context Protocol) server with our professional web interface. Enable AI assistants like Cline, Cursor, and Windsurf to interact with your APIs seamlessly.

## ✨ **Production Ready - Deploy to Railway Now!**

MCPify is a complete, production-ready solution with professional UI, monetization features, and Railway deployment configuration.

## 🚀 Features

### Dual Interface Support
- **CLI Tool**: Perfect for local development and automation
- **REST API**: Ideal for web applications and integrations
- **Web UI**: Modern React interface for MCP server generation
- **Instant Generation**: Quick OpenAPI to MCP server conversion
- **Download Ready**: Complete server bundles with dependencies

### OpenAPI Support
- ✅ OpenAPI 3.0.x and 3.1.x specifications
- ✅ JSON and YAML formats
- ✅ Local files and remote URLs
- ✅ File upload support
- ✅ Comprehensive validation

### MCP Generation
- 🛠️ Automatic tool generation from endpoints
- 📋 Type-safe parameter handling
- 🔄 Request/response schema mapping
- 📦 Complete server scaffolding
- 🗜️ Downloadable ZIP bundles

### Generated Features
- 🔧 Express.js MCP server
- 🎯 Automatic API forwarding
- 🛡️ Error handling and validation
- 📝 TypeScript support
- 🐳 Docker ready

## 📦 Installation

### Global CLI Installation
```bash
npm install -g openapi-to-mcp
```

### Local Development
```bash
git clone https://github.com/yourusername/openapi-to-mcp.git
cd openapi-to-mcp
npm install
npm run build
```

## 🔧 Quick Start

### CLI Usage

```bash
# Convert from local file
openapi-to-mcp ./petstore.yaml

# Convert from URL
openapi-to-mcp https://api.example.com/openapi.json

# Specify output directory
openapi-to-mcp ./api.yaml --output ./my-mcp-server

# Start API server
openapi-to-mcp serve --port 3000
```

### API Usage

```bash
# Start the API server
npm start

# Server will be available at http://localhost:3000
```

#### Convert via JSON payload
```bash
curl -X POST http://localhost:3000/api/convert \
  -H "Content-Type: application/json" \
  -d '{
    "openapi": "https://petstore3.swagger.io/api/v3/openapi.json",
    "config": {
      "name": "petstore-mcp",
      "version": "1.0.0",
      "port": 8000
    }
  }'
```

#### Convert via file upload
```bash
curl -X POST http://localhost:3000/api/convert/upload \
  -F "openapi=@./petstore.yaml" \
  -F 'config={"name":"my-api","port":8000}'
```

## 📋 API Reference

### POST /api/convert
Convert OpenAPI specification to MCP bundle.

**Request Body:**
```json
{
  "openapi": "URL | YAML string | JSON object",
  "config": {
    "name": "my-api-mcp",
    "version": "1.0.0",
    "baseUrl": "https://api.example.com",
    "port": 8000,
    "author": "Your Name",
    "license": "MIT"
  }
}
```

**Response:**
```json
{
  "success": true,
  "bundleId": "uuid-bundle-id",
  "manifest": { ... },
  "serverCode": "// Generated server code",
  "files": { ... },
  "downloadUrl": "/api/download/uuid-bundle-id",
  "stats": {
    "endpoints": 5,
    "tools": 5,
    "generationTime": "1.2s"
  }
}
```

### POST /api/convert/upload
Convert OpenAPI specification via file upload.

**Form Data:**
- `openapi`: OpenAPI file (JSON/YAML)
- `config`: JSON string with configuration

### GET /api/download/:bundleId
Download generated MCP server bundle as ZIP file.

### GET /api/health
Health check endpoint.

**Response:**
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "uptime": "2h 15m 30s"
}
```

## 🛠️ Development

### Prerequisites
- Node.js 18+
- npm or yarn

### Setup
```bash
npm install
npm run dev          # Start API server in development
npm run dev:cli      # CLI development mode
npm test             # Run tests
npm run test:watch   # Watch mode
npm run build        # Build TypeScript
```

### Project Structure
```
openapi-to-mcp/
├── src/                          # Backend source code
│   ├── cli.ts                    # CLI entry point
│   ├── server.ts                 # API server entry point
│   ├── core/                     # Core conversion logic
│   │   ├── openapiParser.ts      # OpenAPI parser
│   │   ├── mcpManifestGenerator.ts # MCP manifest generator
│   │   ├── serverGenerator.ts    # Server code generator
│   │   └── bundleGenerator.ts    # ZIP bundle creator
│   ├── routes/                   # API routes
│   ├── middleware/               # Express middleware
│   ├── types.ts                  # TypeScript definitions
│   └── utils.ts                  # Utility functions
├── ui/                           # Simple web interface for server generation
│   ├── src/                      # React source code
│   │   ├── components/           # UI components
│   │   ├── pages/                # Page components
│   │   └── services/             # API services
│   └── public/                   # Static assets
├── tests/                        # Test suites
└── templates/                    # Code generation templates
```

## 📝 Examples

### Simple Ping API
```yaml
openapi: 3.0.0
info:
  title: Ping API
  version: 1.0.0
servers:
  - url: http://localhost:3000
paths:
  /ping:
    get:
      operationId: ping
      summary: Ping endpoint
      responses:
        '200':
          description: Pong response
          content:
            text/plain:
              schema:
                type: string
                example: pong
```

**Generated MCP Tool:**
```json
{
  "name": "ping",
  "description": "Ping endpoint",
  "inputSchema": {
    "type": "object",
    "properties": {},
    "description": "No input parameters required"
  },
  "outputSchema": {
    "type": "string",
    "description": "Pong response"
  }
}
```

### Generated Server Usage
After conversion, you can run the generated MCP server:

```bash
cd generated-mcp-server
npm install
npm start
# Server runs on specified port (default: 8000)
```

## 💬 Using Your Generated MCP Server

### Recommended MCP Client: ChatMCP

We recommend using **[ChatMCP](https://github.com/daodao97/chatmcp)** to interact with your generated MCP servers. ChatMCP is a cross-platform, open-source MCP client specifically designed for MCP workflows.

#### Why ChatMCP?
- 🎯 **MCP-native**: Built specifically for MCP server interactions
- 🌍 **Cross-platform**: Available for macOS, Windows, Linux, iOS, and Android
- 🔧 **Auto-installation**: Automatically installs and manages MCP servers
- 🏪 **Server marketplace**: Discover and install community MCP servers
- 🔄 **SSE support**: Modern transport protocols
- 🌐 **Multi-language**: Supports multiple languages

#### Installation

**Desktop Applications:**
- **macOS**: [Download from Releases](https://github.com/daodao97/chatmcp/releases)
- **Windows**: [Download from Releases](https://github.com/daodao97/chatmcp/releases)
- **Linux**: [Download from Releases](https://github.com/daodao97/chatmcp/releases)

**Mobile Applications:**
- **iOS**: [TestFlight](https://testflight.apple.com/join/dCXksFJV)
- **Android**: [Download APK](https://github.com/daodao97/chatmcp/releases)

#### Connecting Your Generated Server

1. **Start your generated MCP server:**
   ```bash
   cd your-generated-server
   npm start
   ```

2. **Open ChatMCP** and go to **MCP Server** settings

3. **Add your server** with these settings:
   ```json
   {
     "name": "Your API Server",
     "command": "node",
     "args": ["path/to/your-generated-server/dist/server.js"],
     "env": {
       "PORT": "8000",
       "BASE_URL": "https://your-api-endpoint.com"
     }
   }
   ```

4. **Start chatting** with your API through natural language!

#### Example Interaction
```
You: "Find all available pets"
ChatMCP: *calls findPetsByStatus tool*
Response: "Found 15 pets: Buddy, Max, Luna, Charlie..."

You: "Get details for pet ID 123"
ChatMCP: *calls getPetById tool*
Response: "Pet Details: Name: Buddy, Status: Available, Category: Dog..."
```

### Alternative MCP Clients

While we recommend ChatMCP, your generated servers work with any MCP-compatible client:

- **[Claude Desktop](https://claude.ai/download)** - Anthropic's official client
- **[Dolphin-MCP](https://github.com/cognitivecomputations/dolphin-mcp)** - Python CLI client
- **[Open MCP Client](https://github.com/CopilotKit/open-mcp-client)** - Web-based client
- **[LibreChat](https://github.com/danny-avila/LibreChat)** - Self-hosted chat platform

## 🧪 Testing

The project includes comprehensive tests:

```bash
# Run all tests
npm test

# Run with coverage
npm run test:coverage

# Watch mode
npm run test:watch
```

### Test Coverage
- ✅ OpenAPI parsing (JSON/YAML, local/remote)
- ✅ MCP manifest generation
- ✅ Server code generation
- ✅ API endpoints
- ✅ Error handling
- ✅ File uploads

## 🐳 Docker Support

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
COPY templates ./templates
EXPOSE 3000
CMD ["npm", "start"]
```

```bash
# Build and run
docker build -t openapi-to-mcp .
docker run -p 3000:3000 openapi-to-mcp
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Workflow
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

### Coding Standards
- TypeScript with strict mode
- ESLint configuration
- Prettier formatting
- Jest testing
- Conventional commits

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🔗 Links

- [GitHub Repository](https://github.com/yourusername/openapi-to-mcp)
- [npm Package](https://www.npmjs.com/package/openapi-to-mcp)
- [Issue Tracker](https://github.com/yourusername/openapi-to-mcp/issues)
- [MCP Documentation](https://docs.modelcontextprotocol.org/)
- [OpenAPI Specification](https://swagger.io/specification/)

## 📞 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/example)
- 📖 Documentation: [Full docs](https://docs.openapi-to-mcp.com)
- 🐛 Bug Reports: [GitHub Issues](https://github.com/yourusername/openapi-to-mcp/issues)

---

**Made with ❤️ by the OpenAPI-to-MCP team**
