/**
 * MCP server code generator
 */

import * as fs from 'fs-extra';
import * as path from 'path';
import Handlebars from 'handlebars';
import {
  ParsedOpenAPI,
  ParsedEndpoint,
  ServerConfig,
  ServerGenerationError
} from '../types';
import {
  sanitizeIdentifier,
  ensureDir
} from '../utils';

export class ServerGenerator {
  /**
   * Extract path parameters from OpenAPI path
   */
  private extractPathParams(path: string): string[] {
    const matches = path.match(/\{([^}]+)\}/g);
    return matches ? matches.map(match => match.slice(1, -1)) : [];
  }

  /**
   * Generate complete MCP server with all files
   */
  async generateServer(parsed: ParsedOpenAPI, config: ServerConfig): Promise<Record<string, string>> {
    try {
      const files: Record<string, string> = {};

      // Generate main server file
      files['src/server.ts'] = this.generateServerCode(parsed, config);
      
      // Generate types file
      let typesFile = this.generateTypesFile(parsed);
      // Fix any lingering tags?: array; issues from OpenAPI schemas
      typesFile = typesFile.replace(/tags\?:\s*array;/g, 'tags?: any[];');
      files['src/types.ts'] = typesFile;
      
      // Generate route handlers
      files['src/routes.ts'] = this.generateRoutesFile(parsed, config);
      
      // Generate package.json
      files['package.json'] = this.generatePackageJson(config);
      
      // Generate tsconfig.json
      files['tsconfig.json'] = this.generateTsConfig();
      
      // Generate README for the generated server
      files['README.md'] = this.generateServerReadme(parsed, config);
      
      // Generate environment file
      files['.env.example'] = this.generateEnvFile(config);

      // Generate stdio test script
      files['test-stdio.js'] = this.generateStdioTestScript();

      return files;
    } catch (error) {
      throw new ServerGenerationError(
        'Failed to generate MCP server',
        error as Error
      );
    }
  }

  /**
   * Generate main server TypeScript code
   */
  private generateServerCode(parsed: ParsedOpenAPI, config: ServerConfig): string {
    const template = `
import dotenv from 'dotenv';
import * as path from 'path';
import fs from 'fs';

// Try to load .env from both root and dist (for manual runs)
const envPathRoot = path.resolve(__dirname, '../.env');
const envPathDist = path.resolve(__dirname, '../../.env');
if (fs.existsSync(envPathRoot)) {
  dotenv.config({ path: envPathRoot });
} else if (fs.existsSync(envPathDist)) {
  dotenv.config({ path: envPathDist });
} else {
  dotenv.config();
}

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import axios from 'axios';
import { router as apiRoutes } from './routes';

const app = express();
const PORT = process.env.PORT || {{port}};
const BASE_URL = process.env.BASE_URL || '{{baseUrl}}';

// Security middleware
app.use(helmet());
app.use(cors());

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check
app.get('/health', (req: express.Request, res: express.Response) => {
  res.json({
    status: 'healthy',
    name: '{{name}}',
    version: '{{version}}',
    timestamp: new Date().toISOString(),
    baseUrl: BASE_URL
  });
});

// MCP tool endpoints
app.use('/tools', apiRoutes);

// MCP Protocol Types
interface MCPRequest {
  jsonrpc: '2.0';
  id?: string | number;
  method: string;
  params?: any;
}

interface MCPResponse {
  jsonrpc: '2.0';
  id?: string | number;
  result?: any;
  error?: {
    code: number;
    message: string;
    data?: any;
  };
}

// MCP Server Info
const SERVER_INFO = {
  name: '{{name}}',
  version: '{{version}}',
  description: 'Generated MCP server from OpenAPI specification',
  author: 'openapi-to-mcp'
};

// Available tools from OpenAPI spec
const AVAILABLE_TOOLS = {{{toolsList}}};

// MCP Protocol Handler - supports both stdio and HTTP
app.post('/mcp', async (req: express.Request, res: express.Response) => {
  try {
    const request: MCPRequest = req.body;

    // Validate JSON-RPC 2.0 format
    if (request.jsonrpc !== '2.0') {
      return res.status(400).json({
        jsonrpc: '2.0',
        id: request.id,
        error: {
          code: -32600,
          message: 'Invalid Request - must be JSON-RPC 2.0'
        }
      });
    }

    const response = await handleMCPRequest(request);
    res.json(response);
  } catch (error: any) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    res.status(500).json({
      jsonrpc: '2.0',
      id: req.body?.id,
      error: {
        code: -32603,
        message: 'Internal error',
        data: errorMessage
      }
    });
  }
});

// SSE endpoint for streaming MCP protocol
app.get('/mcp/sse', (req: express.Request, res: express.Response) => {
  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Cache-Control'
  });

  // Send initial connection event
  res.write('data: {"jsonrpc":"2.0","method":"connection","params":{"status":"connected"}}\\n\\n');

  // Handle client disconnect
  req.on('close', () => {
    res.end();
  });
});

// MCP Request Handler
async function handleMCPRequest(request: MCPRequest): Promise<MCPResponse> {
  const { method, params, id } = request;

  try {
    switch (method) {
      case 'initialize':
        return {
          jsonrpc: '2.0',
          id,
          result: {
            protocolVersion: '2024-11-05',
            capabilities: {
              tools: {},
              logging: {},
              prompts: {},
              resources: {}
            },
            serverInfo: SERVER_INFO
          }
        };

      case 'tools/list':
        return {
          jsonrpc: '2.0',
          id,
          result: {
            tools: AVAILABLE_TOOLS
          }
        };

      case 'tools/call':
        const { name: toolName, arguments: toolArgs } = params;

        // Validate tool exists
        const tool = AVAILABLE_TOOLS.find((t: any) => t.name === toolName);
        if (!tool) {
          return {
            jsonrpc: '2.0',
            id,
            error: {
              code: -32602,
              message: \`Tool '\${toolName}' not found\`
            }
          };
        }

        // Call the tool
        const toolResult = await callTool(toolName, toolArgs);

        return {
          jsonrpc: '2.0',
          id,
          result: {
            content: [
              {
                type: 'text',
                text: JSON.stringify(toolResult, null, 2)
              }
            ]
          }
        };

      case 'ping':
        return {
          jsonrpc: '2.0',
          id,
          result: {}
        };

      default:
        return {
          jsonrpc: '2.0',
          id,
          error: {
            code: -32601,
            message: \`Method '\${method}' not found\`
          }
        };
    }
  } catch (error: any) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return {
      jsonrpc: '2.0',
      id,
      error: {
        code: -32603,
        message: 'Internal error',
        data: errorMessage
      }
    };
  }
}

// Tool execution function
async function callTool(toolName: string, args: any): Promise<any> {
  try {
    // In stdio mode, call the tool functions directly
    if (isStdioMode) {
      // Call the tool function directly based on the tool name
      switch (toolName) {
        {{#each endpoints}}
        case '{{operationId}}':
          return await execute{{operationId}}(args);
        {{/each}}
        default:
          throw new Error(\`Unknown tool: \${toolName}\`);
      }
    } else {
      // HTTP mode - make actual HTTP request
      const response = await fetch(\`http://localhost:\${PORT}/tools/\${toolName}\`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(args)
      });

      if (!response.ok) {
        throw new Error(\`Tool call failed: \${response.status} \${response.statusText}\`);
      }

      return await response.json();
    }
  } catch (error: any) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new Error(\`Failed to execute tool '\${toolName}': \${errorMessage}\`);
  }
}

// MCP Tools endpoint - lists available tools for MCP clients
app.get('/tools', (req: express.Request, res: express.Response) => {
  try {
    // Load tools from types file if available
    let openApiTools: any[] = [];
    try {
      openApiTools = require('./types').openApiTools || [];
    } catch (error) {
      console.log('[MCP] No types file found, returning empty tools list');
    }

    res.json({
      tools: openApiTools,
      count: openApiTools.length,
      server: {
        name: '{{name}}',
        version: '{{version}}',
        baseUrl: BASE_URL
      }
    });
  } catch (error: any) {
    console.error('[MCP] Error loading tools:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    res.status(500).json({
      error: 'Failed to load tools',
      details: errorMessage
    });
  }
});

// Error handling
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Server error:', error);
  res.status(500).json({
    error: 'Internal server error',
    message: error instanceof Error ? error.message : String(error)
  });
});

// 404 handler
app.use('*', (req: express.Request, res: express.Response) => {
  res.status(404).json({
    error: 'Not found',
    message: \`Route \${req.originalUrl} not found\`
  });
});

// Individual tool execution functions for stdio mode
{{#each endpoints}}
async function execute{{operationId}}(args: any): Promise<any> {
  // Build URL with path parameters
  let url = \`\${BASE_URL}{{path}}\`;
  {{#each parameters}}
  {{#if (eq in 'path')}}
  url = url.replace('{{{name}}}', encodeURIComponent(args.{{name}}));
  {{/if}}
  {{/each}}

  // Prepare request configuration
  const config: any = {
    method: '{{method}}',
    url,
    timeout: 30000,
    headers: {
      'Accept': 'application/json'
    }
  };

  {{#if (ne method 'GET')}}
  // Add request body for non-GET requests
  if (args.body) {
    config.data = args.body;
    config.headers['Content-Type'] = 'application/json';
  }
  {{/if}}

  // Add query parameters
  {{#each parameters}}
  {{#if (eq in 'query')}}
  if (args.query?.{{name}} !== undefined) {
    config.params = config.params || {};
    {{#if (eq schema.type 'array')}}
    config.params.{{name}} = Array.isArray(args.query.{{name}}) ? args.query.{{name}} : [args.query.{{name}}];
    {{else}}
    config.params.{{name}} = args.query.{{name}};
    {{/if}}
  }
  {{/if}}
  {{/each}}

  // Add headers
  {{#each parameters}}
  {{#if (eq in 'header')}}
  if (args.headers?.{{name}}) {
    config.headers['{{name}}'] = args.headers.{{name}};
  }
  {{/if}}
  {{/each}}

  // Make API request
  const response = await axios(config);
  return { success: true, data: response.data };
}

{{/each}}

// Check if running in stdio mode (for MCP clients like Cline)
// Multiple detection methods for better reliability
const isStdioMode = (
  process.stdin.isTTY === false ||  // Standard detection
  process.argv.includes('--stdio') ||  // Explicit flag
  process.env.MCP_STDIO_MODE === 'true' ||  // Environment variable
  !process.stdout.isTTY  // Output is being piped
);

if (isStdioMode) {
  // STDIO MODE: Handle MCP protocol via stdin/stdout
  console.error(\`🔌 MCP Server starting in stdio mode for \${AVAILABLE_TOOLS.length} tools\`);
  console.error(\`🔧 Detection: stdin.isTTY=\${process.stdin.isTTY}, stdout.isTTY=\${process.stdout.isTTY}\`);

  // Handle stdio for MCP clients (Cline, Cursor, Windsurf, etc.)
  let buffer = '';

  // Set stdin to raw mode for better handling
  if (process.stdin.setRawMode) {
    process.stdin.setRawMode(false);
  }
  process.stdin.setEncoding('utf8');

  process.stdin.on('data', async (chunk) => {
    try {
      buffer += chunk.toString();

      // Process complete JSON messages (handle both \\n and \\r\\n)
      const lines = buffer.split(/\\r?\\n/);
      buffer = lines.pop() || '';

      for (const line of lines) {
        const trimmedLine = line.trim();
        if (trimmedLine) {
          console.error(\`📥 Received: \${trimmedLine.substring(0, 100)}...\`);

          try {
            const request: MCPRequest = JSON.parse(trimmedLine);
            const response = await handleMCPRequest(request);
            const responseStr = JSON.stringify(response);

            console.error(\`📤 Sending: \${responseStr.substring(0, 100)}...\`);
            process.stdout.write(responseStr + '\\n');

          } catch (parseError) {
            const errorMessage = parseError instanceof Error ? parseError.message : String(parseError);
            console.error(\`❌ Parse error: \${errorMessage}\`);
            const errorResponse: MCPResponse = {
              jsonrpc: '2.0',
              error: {
                code: -32700,
                message: 'Parse error',
                data: errorMessage
              }
            };
            process.stdout.write(JSON.stringify(errorResponse) + '\\n');
          }
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(\`❌ Stdin error: \${errorMessage}\`);
    }
  });

  process.stdin.on('end', () => {
    process.exit(0);
  });

} else {
  // HTTP MODE: Start HTTP server for web-based clients
  const server = app.listen(PORT, () => {
    console.log(\`🚀 {{name}} MCP Server running on port \${PORT}\`);
    console.log(\`📋 Health check: http://localhost:\${PORT}/health\`);
    console.log(\`🔧 MCP endpoint: http://localhost:\${PORT}/mcp\`);
    console.log(\`📡 SSE endpoint: http://localhost:\${PORT}/mcp/sse\`);
    console.log(\`🛠️  Tools endpoint: http://localhost:\${PORT}/tools\`);
    console.log(\`🌐 Base URL: \${BASE_URL}\`);
    console.log(\`\`);
    console.log(\`📖 Available tools: \${AVAILABLE_TOOLS.length}\`);
    AVAILABLE_TOOLS.forEach((tool: any) => {
      console.log(\`   - \${tool.name}: \${tool.description}\`);
    });
    console.log(\`\`);
    console.log(\`🔌 MCP Client Configuration:\`);
    console.log(\`   stdio: node \${__filename.replace('.ts', '.js')}\`);
    console.log(\`   http: POST http://localhost:\${PORT}/mcp\`);
    console.log(\`   sse: GET http://localhost:\${PORT}/mcp/sse\`);
  });

  // Graceful shutdown for HTTP mode
  process.on('SIGINT', () => {
    console.log('\\n🛑 Shutting down MCP server...');
    server.close(() => {
      process.exit(0);
    });
  });

  process.on('SIGTERM', () => {
    console.log('\\n🛑 Shutting down MCP server...');
    server.close(() => {
      process.exit(0);
    });
  });
}

export default app;
`;

    const compiledTemplate = Handlebars.compile(template);
    return compiledTemplate({
      name: config.name,
      version: config.version,
      port: config.port,
      baseUrl: config.baseUrl,
      toolsList: JSON.stringify(this.generateToolsList(parsed), null, 2),
      endpoints: parsed.endpoints.map(endpoint => ({
        operationId: sanitizeIdentifier(endpoint.operationId),
        method: endpoint.method.toUpperCase(),
        path: endpoint.path,
        parameters: endpoint.parameters
      }))
    });
  }

  /**
   * Generate MCP tools list from OpenAPI endpoints
   */
  private generateToolsList(parsed: ParsedOpenAPI): any[] {
    return parsed.endpoints.map(endpoint => {
      const params: any = {};
      const required: string[] = [];

      // Add path parameters
      for (const param of endpoint.parameters.filter(p => p.in === 'path')) {
        params[param.name] = {
          type: param.schema?.type || 'string',
          description: param.description || `Path parameter: ${param.name}`
        };
        if (param.required) required.push(param.name);
      }

      // Add query parameters as a nested object
      const queryParams = endpoint.parameters.filter(p => p.in === 'query');
      if (queryParams.length > 0) {
        const queryProps: any = {};
        for (const param of queryParams) {
          queryProps[param.name] = {
            type: param.schema?.type || 'string',
            description: param.description || `Query parameter: ${param.name}`
          };
        }
        params['query'] = {
          type: 'object',
          description: 'Query parameters',
          properties: queryProps
        };
      }

      // Add headers if any
      const headerParams = endpoint.parameters.filter(p => p.in === 'header');
      if (headerParams.length > 0) {
        const headerProps: any = {};
        for (const param of headerParams) {
          headerProps[param.name] = {
            type: 'string',
            description: param.description || `Header: ${param.name}`
          };
        }
        params['headers'] = {
          type: 'object',
          description: 'HTTP headers',
          properties: headerProps
        };
      }

      // Add request body if exists
      if (endpoint.requestBody) {
        params['body'] = {
          type: 'object',
          description: endpoint.requestBody.description || 'Request body'
        };
        if (endpoint.requestBody.required) required.push('body');
      }

      return {
        name: endpoint.operationId,
        description: endpoint.summary || endpoint.description || `${endpoint.method.toUpperCase()} ${endpoint.path}`,
        inputSchema: {
          type: "object",
          properties: params,
          required
        }
      };
    });
  }

  /**
   * Generate routes file with tool handlers
   */
  private generateRoutesFile(parsed: ParsedOpenAPI, config: ServerConfig): string {
    const routes: string[] = [];
    
    for (const endpoint of parsed.endpoints) {
      const routeCode = this.generateRouteHandler(endpoint, config.baseUrl);
      routes.push(routeCode);
    }

    const template = `
import { Router, Request, Response } from 'express';
import axios from 'axios';

const router = Router();
const BASE_URL = process.env.BASE_URL || '{{baseUrl}}';

{{#each routes}}
{{{this}}}

{{/each}}

// Generic LLM alias endpoints for robustness across all APIs
// These handle common LLM mistakes and provide better user experience

// Catch-all for missing endpoints - try to find a close match
router.use('*', async (req: Request, res: Response, next: any) => {
  if (req.method !== 'POST') return next();
  
  const requestedPath = req.path.replace('/tools/', '').replace('/', '');
  
  // Get all actual endpoint names from this router
  const actualEndpoints = router.stack
    .filter((layer: any) => layer.route && layer.route.path !== '*')
    .map((layer: any) => layer.route.path.replace('/', ''));
  
  // Try to find a close match using common patterns
  let bestMatch = null;
  
  // Common LLM naming patterns and their likely targets
  const patterns = [
    // "find" variations
    { pattern: /^(find|list|get|search|query)(.+)$/, target: (match: string) => actualEndpoints.find(ep => ep.includes(match.toLowerCase())) },
    
    // "create" variations  
    { pattern: /^(add|insert|new|create)(.+)$/, target: (match: string) => actualEndpoints.find(ep => ep.includes('create') || ep.includes('add')) },
    
    // "update" variations
    { pattern: /^(edit|modify|change|update)(.+)$/, target: (match: string) => actualEndpoints.find(ep => ep.includes('update') || ep.includes('edit')) },
    
    // "delete" variations
    { pattern: /^(remove|destroy|delete)(.+)$/, target: (match: string) => actualEndpoints.find(ep => ep.includes('delete') || ep.includes('remove')) }
  ];
  
  for (const { pattern, target } of patterns) {
    const match = requestedPath.match(pattern);
    if (match) {
      const baseWord = match[2];
      bestMatch = target(baseWord) || actualEndpoints.find(ep => 
        ep.toLowerCase().includes(baseWord.toLowerCase()) || 
        baseWord.toLowerCase().includes(ep.toLowerCase().split(/(?=[A-Z])/).join('').toLowerCase())
      );
      if (bestMatch) break;
    }
  }
  
  // If no pattern match, try exact substring matching
  if (!bestMatch) {
    bestMatch = actualEndpoints.find(ep => 
      ep.toLowerCase().includes(requestedPath.toLowerCase()) ||
      requestedPath.toLowerCase().includes(ep.toLowerCase())
    );
  }
  
  if (bestMatch) {
    console.log(\`[ALIAS] Redirecting /\${requestedPath} to /\${bestMatch}\`);
    req.url = \`/\${bestMatch}\`;
    // req.path is read-only in Express, so do not assign to it!
    return next('route');
  }
  
  // If no match found, return helpful error with available endpoints
  res.status(404).json({
    error: 'Tool not found',
    message: \`Tool '\${requestedPath}' not found.\`,
    availableTools: actualEndpoints,
    suggestion: \`Did you mean one of: \${actualEndpoints.slice(0, 5).join(', ')}?\`
  });
});

export { router };
export default router;
`;

    const compiledTemplate = Handlebars.compile(template);
    return compiledTemplate({
      baseUrl: config.baseUrl,
      routes
    });
  }

  /**
   * Generate individual route handler
   */
  private generateRouteHandler(endpoint: ParsedEndpoint, baseUrl: string): string {
    const toolName = sanitizeIdentifier(endpoint.operationId);
    const method = endpoint.method.toLowerCase();
    const path = endpoint.path;
    const pathParams = this.extractPathParams(path);

    const template = `
// {{summary}}
router.post('/{{toolName}}', async (req: Request, res: Response) => {
  try {
    const { {{paramDestructure}} } = req.body;
    
    // Build URL with path parameters
    let url = \`\${BASE_URL}{{path}}\`;
    {{#each pathParams}}
    url = url.replace('{{{name}}}', encodeURIComponent({{name}}));
    {{/each}}
    
    // Prepare request configuration
    const config: any = {
      method: '{{method}}',
      url,
      timeout: 30000
    };
    
    {{#if hasQuery}}
    // Add query parameters
    if (query) {
      config.params = query;
    }
    {{/if}}
    
    {{#if hasHeaders}}
    // Add headers
    if (headers) {
      config.headers = { ...config.headers, ...headers };
    }
    {{/if}}
    
    {{#if hasBody}}
    // Add request body
    if (body) {
      config.data = body;
      config.headers = { 
        ...config.headers, 
        'Content-Type': '{{contentType}}' 
      };
    }
    {{/if}}
    
    // Make API request
    const response = await axios(config);
    
    res.json({
      success: true,
      data: response.data,
      status: response.status,
      headers: response.headers
    });
    
  } catch (error: any) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error('{{toolName}} error:', errorMessage);

    if (error.response) {
      // API error
      res.status(error.response.status || 500).json({
        success: false,
        error: 'API Error',
        message: error.response.data?.message || errorMessage,
        status: error.response.status,
        data: error.response.data
      });
    } else {
      // Network or other error
      res.status(500).json({
        success: false,
        error: 'Request Failed',
        message: errorMessage
      });
    }
  }
});`;

    // Determine parameters
    const params: string[] = [];
    if (pathParams.length > 0) {
      params.push(...pathParams);
    }
    
    const hasQuery = endpoint.parameters.some(p => p.in === 'query');
    const hasHeaders = endpoint.parameters.some(p => p.in === 'header');
    const hasBody = !!endpoint.requestBody;
    
    if (hasQuery) params.push('query');
    if (hasHeaders) params.push('headers');
    if (hasBody) params.push('body');

    const compiledTemplate = Handlebars.compile(template);
    return compiledTemplate({
      toolName,
      method: method.toUpperCase(),
      path,
      pathParams: pathParams.map((name: string) => ({ name })),
      paramDestructure: params.join(', '),
      hasQuery,
      hasHeaders,
      hasBody,
      contentType: endpoint.requestBody?.contentType || 'application/json',
      summary: endpoint.summary || `${method.toUpperCase()} ${path}`
    });
  }

  /**
   * Generate types file
   */
  private generateTypesFile(parsed: ParsedOpenAPI): string {
    // Generate OpenAPI tool definitions for LLM function calling
    const openApiTools = parsed.endpoints.map(endpoint => {
      const params: any = {};
      const required: string[] = [];
      
      // Add path parameters
      for (const param of endpoint.parameters.filter(p => p.in === 'path')) {
        params[param.name] = {
          type: param.schema?.type || 'string',
          description: param.description || `Path parameter: ${param.name}`
        };
        if (param.required) required.push(param.name);
      }
      
      // Add query parameters as a nested object
      const queryParams = endpoint.parameters.filter(p => p.in === 'query');
      if (queryParams.length > 0) {
        const queryProps: any = {};
        for (const param of queryParams) {
          queryProps[param.name] = {
            type: param.schema?.type || 'string',
            description: param.description || `Query parameter: ${param.name}`
          };
        }
        params['query'] = {
          type: 'object',
          description: 'Query parameters',
          properties: queryProps
        };
      }
      
      // Add headers if any
      const headerParams = endpoint.parameters.filter(p => p.in === 'header');
      if (headerParams.length > 0) {
        const headerProps: any = {};
        for (const param of headerParams) {
          headerProps[param.name] = {
            type: 'string',
            description: param.description || `Header: ${param.name}`
          };
        }
        params['headers'] = {
          type: 'object',
          description: 'HTTP headers',
          properties: headerProps
        };
      }
      
      // Add request body if exists
      if (endpoint.requestBody) {
        params['body'] = { 
          type: 'object', 
          description: endpoint.requestBody.description || 'Request body'
        };
        if (endpoint.requestBody.required) required.push('body');
      }

      // Enhance description for better LLM understanding
      let enhancedDescription = endpoint.summary || endpoint.description || `${endpoint.method.toUpperCase()} ${endpoint.path}`;

      // Add specific keywords for common operations to help LLM matching
      if (endpoint.operationId.toLowerCase().includes('find') && endpoint.operationId.toLowerCase().includes('pets')) {
        enhancedDescription += '. Use this to find, list, search, or get pets by their status (available, pending, sold).';
      } else if (endpoint.operationId.toLowerCase().includes('find') && endpoint.operationId.toLowerCase().includes('status')) {
        enhancedDescription += '. Use this to find, list, search, or get items by their status.';
      } else if (endpoint.operationId.toLowerCase().includes('get') && endpoint.operationId.toLowerCase().includes('pet')) {
        enhancedDescription += '. Use this to get, find, or retrieve a specific pet by ID.';
      }

      return {
        type: "function",
        function: {
          name: endpoint.operationId,
          description: enhancedDescription,
          parameters: {
            type: "object",
            properties: params,
            required
          }
        }
      };
    });

    return `
// Generated types for MCP server

export interface ToolRequest {
  [key: string]: any;
}

export interface ToolResponse {
  success: boolean;
  data?: any;
  error?: string;
  message?: string;
  status?: number;
}

export interface HealthResponse {
  status: string;
  name: string;
  version: string;
  timestamp: string;
  baseUrl: string;
}

export interface MCPManifest {
  // ...other fields...
  tags?: any[];
}

// OpenAPI tool definitions for LLM function calling
export const openApiTools = ${JSON.stringify(openApiTools, null, 2)};

// OpenAPI derived types
${this.generateEndpointTypes(parsed.endpoints)}
`;
  }

  /**
   * Generate TypeScript types for endpoints
   */
  private generateEndpointTypes(endpoints: ParsedEndpoint[]): string {
    const types: string[] = [];

    for (const endpoint of endpoints) {
      const typeName = sanitizeIdentifier(endpoint.operationId) + 'Request';
      const properties: string[] = [];

      // Path parameters
      const pathParams = endpoint.parameters.filter(p => p.in === 'path');
      if (pathParams.length > 0) {
        for (const param of pathParams) {
          const optional = param.required ? '' : '?';
          properties.push(`  ${param.name}${optional}: string;`);
        }
      }

      // Query parameters
      const queryParams = endpoint.parameters.filter(p => p.in === 'query');
      if (queryParams.length > 0) {
        properties.push('  query?: {');
        for (const param of queryParams) {
          const optional = param.required ? '' : '?';
          const type = param.schema.type || 'any';
          properties.push(`    ${param.name}${optional}: ${type};`);
        }
        properties.push('  };');
      }

      // Headers
      const headerParams = endpoint.parameters.filter(p => p.in === 'header');
      if (headerParams.length > 0) {
        properties.push('  headers?: {');
        for (const param of headerParams) {
          const optional = param.required ? '' : '?';
          properties.push(`    ${param.name}${optional}: string;`);
        }
        properties.push('  };');
      }

      // Request body
      if (endpoint.requestBody) {
        const optional = endpoint.requestBody.required ? '' : '?';
        properties.push(`  body${optional}: any;`);
      }

      // Tags (fix for tags?: array)
      if ((endpoint as any).tags) {
        properties.push('  tags?: any[];');
      }

      if (properties.length > 0) {
        types.push(`export interface ${typeName} {
${properties.join('\n')}
}`);
      }
    }

    return types.join('\n\n');
  }

  /**
   * Generate package.json for the generated server
   */
  private generatePackageJson(config: ServerConfig): string {
    const packageJson = {
      name: config.name,
      version: config.version,
      description: config.description,
      main: 'dist/server.js',
      scripts: {
        build: 'tsc',
        start: 'node dist/server.js',
        'start:stdio': 'cross-env MCP_STDIO_MODE=true node dist/server.js',
        dev: 'ts-node src/server.ts',
        test: 'echo "No tests specified" && exit 0'
      },
      dependencies: {
        express: '^4.18.2',
        cors: '^2.8.5',
        helmet: '^7.1.0',
        axios: '^1.6.2',
        dotenv: '^16.3.1'
      },
      devDependencies: {
        '@types/node': '^20.10.4',
        '@types/express': '^4.17.21',
        '@types/cors': '^2.8.17',
        typescript: '^5.3.3',
        'ts-node': '^10.9.1',
        'cross-env': '^7.0.3'
      },
      engines: {
        node: '>=18.0.0'
      },
      keywords: ['mcp', 'server', 'api', 'openapi'],
      author: config.author || 'Generated by openapi-to-mcp',
      license: config.license || 'MIT'
    };

    return JSON.stringify(packageJson, null, 2);
  }

  /**
   * Generate TypeScript configuration
   */
  private generateTsConfig(): string {
    const tsConfig = {
      compilerOptions: {
        target: 'ES2020',
        module: 'commonjs',
        lib: ['ES2020'],
        outDir: './dist',
        rootDir: './src',
        strict: true,
        esModuleInterop: true,
        skipLibCheck: true,
        forceConsistentCasingInFileNames: true,
        resolveJsonModule: true,
        declaration: true,
        sourceMap: true
      },
      include: ['src/**/*'],
      exclude: ['node_modules', 'dist']
    };

    return JSON.stringify(tsConfig, null, 2);
  }

  /**
   * Generate README for the generated server
   */
  private generateServerReadme(parsed: ParsedOpenAPI, config: ServerConfig): string {
    const endpoints = parsed.endpoints.map(e => 
      `- **${e.operationId}**: ${e.method.toUpperCase()} ${e.path}${e.summary ? ` - ${e.summary}` : ''}`
    ).join('\n');

    return `# ${config.name}

Generated MCP server from OpenAPI specification.

## Overview

- **Name**: ${config.name}
- **Version**: ${config.version}
- **Description**: ${config.description}
- **Base URL**: ${config.baseUrl}
- **Port**: ${config.port}

## Available Tools

${endpoints}

## Installation

\`\`\`bash
npm install
npm run build
\`\`\`

## Usage

\`\`\`bash
# Development
npm run dev

# Production
npm start
\`\`\`

## Environment Variables

Create a \`.env\` file based on \`.env.example\`:

\`\`\`bash
PORT=${config.port}
BASE_URL=${config.baseUrl}
NODE_ENV=production
\`\`\`

## MCP Client Configuration

### Cline (Claude Dev)
Add to your Cline MCP configuration:
\`\`\`json
{
  "mcpServers": {
    "${config.name}": {
      "command": "node",
      "args": ["path/to/your/server/dist/server.js"],
      "env": {
        "PORT": "${config.port}",
        "BASE_URL": "${config.baseUrl}"
      }
    }
  }
}
\`\`\`

### Cursor
Add to your Cursor MCP configuration:
\`\`\`json
{
  "mcpServers": {
    "${config.name}": {
      "command": "node",
      "args": ["path/to/your/server/dist/server.js"]
    }
  }
}
\`\`\`

### Windsurf
Add to your Windsurf MCP configuration:
\`\`\`json
{
  "mcpServers": {
    "${config.name}": {
      "command": "node",
      "args": ["path/to/your/server/dist/server.js"]
    }
  }
}
\`\`\`

### ChatMCP
Configure as HTTP transport:
- **Transport**: stdio
- **Command**: node
- **Args**: path/to/your/server/dist/server.js

## API Endpoints

### Health Check
\`\`\`
GET /health
\`\`\`

### MCP Protocol (JSON-RPC 2.0)
\`\`\`
POST /mcp
Content-Type: application/json

{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "toolName",
    "arguments": { ... }
  }
}
\`\`\`

### SSE Streaming
\`\`\`
GET /mcp/sse
\`\`\`

### Direct Tool Access
\`\`\`
POST /tools/{toolName}
Content-Type: application/json

{ "param1": "value1", ... }
\`\`\`

## Generated by

[openapi-to-mcp](https://github.com/yourusername/openapi-to-mcp)
`;
  }

  /**
   * Generate environment file template
   */
  private generateEnvFile(config: ServerConfig): string {
    // Auto-detect common public APIs and set appropriate BASE_URL
    let baseUrl = config.baseUrl;
    if (config.baseUrl.includes('localhost') || config.baseUrl.includes('127.0.0.1')) {
      if (config.name.toLowerCase().includes('petstore') || config.description?.toLowerCase().includes('petstore')) {
        baseUrl = 'https://petstore3.swagger.io/api/v3';
      } else if (config.name.toLowerCase().includes('jsonplaceholder')) {
        baseUrl = 'https://jsonplaceholder.typicode.com';
      }
    }

    return `# Generated MCP Server Environment Configuration

# Server Configuration
PORT=${config.port}
BASE_URL=${baseUrl}
NODE_ENV=development

# API Configuration
# Add any API keys or authentication tokens here
# API_KEY=your_api_key_here
# AUTH_TOKEN=your_auth_token_here

# Logging
LOG_LEVEL=info

# CORS Configuration
# CORS_ORIGIN=http://localhost:3000
`;
  }

  /**
   * Generate stdio test script
   */
  private generateStdioTestScript(): string {
    return `#!/usr/bin/env node

/**
 * Test script for stdio MCP communication
 * Usage: node test-stdio.js
 */

const { spawn } = require('child_process');

console.log('🧪 Testing MCP stdio communication...');

// Test data
const testRequests = [
  {
    jsonrpc: '2.0',
    id: 1,
    method: 'initialize',
    params: {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'test-client', version: '1.0.0' }
    }
  },
  {
    jsonrpc: '2.0',
    id: 2,
    method: 'tools/list',
    params: {}
  }
];

// Spawn server in stdio mode
const server = spawn('node', ['dist/server.js'], {
  stdio: ['pipe', 'pipe', 'inherit'],
  env: { ...process.env, MCP_STDIO_MODE: 'true' }
});

let responseCount = 0;

// Handle responses
server.stdout.on('data', (data) => {
  const responses = data.toString().trim().split('\\n');
  responses.forEach(response => {
    if (response.trim()) {
      try {
        const parsed = JSON.parse(response);
        console.log(\`✅ Response \${++responseCount}:\`, JSON.stringify(parsed, null, 2));
      } catch (error) {
        console.log(\`❌ Invalid JSON:\`, response);
      }
    }
  });
});

// Send test requests
testRequests.forEach((request, index) => {
  setTimeout(() => {
    console.log(\`📤 Sending request \${index + 1}:\`, request.method);
    server.stdin.write(JSON.stringify(request) + '\\n');
  }, index * 1000);
});

// Cleanup
setTimeout(() => {
  server.kill();
  console.log('🏁 Test completed');
}, 5000);
`;
  }
}

export default ServerGenerator;
