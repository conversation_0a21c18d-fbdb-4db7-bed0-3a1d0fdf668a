#!/usr/bin/env node

/**
 * Test the generated MCP server with Cline-like communication
 */

const { spawn } = require('child_process');
const path = require('path');

async function testClineMCP() {
  console.log('🧪 Testing Cline MCP Server Integration...\n');

  const serverPath = path.resolve('./cline-final-petstore/dist/server.js');
  console.log(`📂 Server path: ${serverPath}`);
  
  // Spawn the MCP server exactly like Cline would
  const server = spawn('node', [serverPath, '--stdio'], {
    stdio: ['pipe', 'pipe', 'pipe'],
    env: {
      ...process.env,
      PORT: '8001',
      BASE_URL: 'https://petstore3.swagger.io/api/v3'
    }
  });

  let responseBuffer = '';
  let responses = [];

  // Handle server stdout (MCP responses)
  server.stdout.on('data', (data) => {
    responseBuffer += data.toString();
    
    const lines = responseBuffer.split('\n');
    responseBuffer = lines.pop() || '';
    
    for (const line of lines) {
      if (line.trim()) {
        try {
          const response = JSON.parse(line.trim());
          console.log('📤 MCP Response:', JSON.stringify(response, null, 2));
          responses.push(response);
        } catch (e) {
          console.log('📤 Raw Output:', line.trim());
        }
      }
    }
  });

  // Handle server stderr (logs)
  server.stderr.on('data', (data) => {
    console.log('🔍 Server Log:', data.toString().trim());
  });

  // Wait for server to start
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Cline-style MCP communication sequence
  const mcpSequence = [
    {
      name: 'Initialize',
      message: {
        jsonrpc: '2.0',
        id: 1,
        method: 'initialize',
        params: {
          protocolVersion: '2024-11-05',
          capabilities: {
            tools: {}
          },
          clientInfo: {
            name: 'cline',
            version: '2.0.0'
          }
        }
      }
    },
    {
      name: 'List Tools',
      message: {
        jsonrpc: '2.0',
        id: 2,
        method: 'tools/list',
        params: {}
      }
    },
    {
      name: 'Call findPetsByStatus (available pets)',
      message: {
        jsonrpc: '2.0',
        id: 3,
        method: 'tools/call',
        params: {
          name: 'findPetsByStatus',
          arguments: {
            query: { status: 'available' }
          }
        }
      }
    },
    {
      name: 'Call getPetById (specific pet)',
      message: {
        jsonrpc: '2.0',
        id: 4,
        method: 'tools/call',
        params: {
          name: 'getPetById',
          arguments: {
            petId: 1
          }
        }
      }
    }
  ];

  // Execute MCP sequence
  for (let i = 0; i < mcpSequence.length; i++) {
    const step = mcpSequence[i];
    console.log(`\n🧪 Step ${i + 1}: ${step.name}`);
    console.log('📥 Sending:', JSON.stringify(step.message, null, 2));
    
    server.stdin.write(JSON.stringify(step.message) + '\n');
    
    // Wait for response
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  // Test completed notification (like Cline would send)
  console.log('\n🧪 Step 5: Notification (no response expected)');
  const notification = {
    jsonrpc: '2.0',
    method: 'notifications/initialized',
    params: {}
  };
  console.log('📥 Sending:', JSON.stringify(notification, null, 2));
  server.stdin.write(JSON.stringify(notification) + '\n');
  
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Cleanup
  console.log('\n🛑 Shutting down server...');
  server.stdin.end();
  
  await new Promise((resolve) => {
    server.on('close', (code) => {
      console.log(`✅ Server exited with code: ${code}`);
      resolve();
    });
    
    setTimeout(() => {
      server.kill('SIGTERM');
      resolve();
    }, 3000);
  });

  // Analyze results
  console.log('\n📊 Test Results:');
  console.log(`Total responses: ${responses.length}`);
  
  const successful = responses.filter(r => r.result !== undefined);
  const errors = responses.filter(r => r.error !== undefined);
  
  console.log(`✅ Successful: ${successful.length}`);
  console.log(`❌ Errors: ${errors.length}`);
  
  // Check specific responses
  const initResponse = responses.find(r => r.id === 1);
  const toolsResponse = responses.find(r => r.id === 2);
  const findPetsResponse = responses.find(r => r.id === 3);
  const getPetResponse = responses.find(r => r.id === 4);
  
  console.log('\n🔍 Response Analysis:');
  console.log(`Initialize: ${initResponse ? '✅' : '❌'}`);
  console.log(`Tools List: ${toolsResponse ? '✅' : '❌'}`);
  console.log(`Find Pets: ${findPetsResponse ? '✅' : '❌'}`);
  console.log(`Get Pet: ${getPetResponse ? '✅' : '❌'}`);
  
  if (toolsResponse && toolsResponse.result && toolsResponse.result.tools) {
    console.log(`\n🛠️ Available Tools: ${toolsResponse.result.tools.length}`);
    toolsResponse.result.tools.slice(0, 5).forEach(tool => {
      console.log(`   - ${tool.name}: ${tool.description}`);
    });
  }
  
  const success = successful.length >= 3; // At least init, tools, and one tool call
  console.log(`\n${success ? '🎉' : '❌'} Cline Integration: ${success ? 'READY' : 'FAILED'}`);
  
  return success;
}

testClineMCP().catch(console.error);
