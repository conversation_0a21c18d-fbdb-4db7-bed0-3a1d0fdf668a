{"version": 3, "file": "resolver-functions.js", "sourceRoot": "", "sources": ["../src/resolver-functions.ts"], "names": [], "mappings": ";;;AAAA,+BAA+B;AAM/B;;;GAGG;AACH,SAAgB,uBAAuB,CAAC,MASvC;IACC,MAAM,EACJ,IAAI,EACJ,EAAE,EACF,MAAM,EACN,GAAG,EACH,oBAAoB,EACpB,yBAAyB,EACzB,OAAO,EACP,UAAU,GACX,GAAG,MAAM,CAAC;IACX,MAAM,qBAAqB,GAAG,EAAE,CAAC,2BAA2B,CAC1D,GAAG,EACH,oBAAoB,EACpB,MAAM,CAAC,OAAO,CACf,CAAC;IACF,MAAM,sBAAsB,GAAG,IAAI,GAAG,EAAU,CAAC;IACjD,gFAAgF;IAChF,MAAM,eAAe,GAAG,IAAI,GAAG,EAAU,CAAC;IAE1C,4FAA4F;IAC5F,wHAAwH;IACxH,sIAAsI;IACtI,MAAM,cAAc,GAAG,0CAA0C,CAAC;IAClE,SAAS,eAAe,CAAC,QAAgB;QACvC,MAAM,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3C,IAAI,IAAI;YAAE,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;QACzB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,0EAA0E;IAC1E,SAAS,4BAA4B,CAAC,QAAgB;QACpD,eAAe,CAAC,GAAG,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;IACjD,CAAC;IAED,SAAS,sBAAsB,CAAC,QAAgB;QAC9C,OAAO,eAAe,CAAC,GAAG,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;IACxD,CAAC;IAED,SAAS,uBAAuB,CAAC,QAAgB;QAC/C,OAAO,sBAAsB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,MAAM,mBAAmB,GAAG,CAC1B,cAE2C,EAC3C,EAAE;QACF,MAAM,EAAE,gBAAgB,EAAE,GAAG,cAAc,CAAC;QAC5C,IAAI,gBAAgB,KAAK,SAAS;YAAE,OAAO;QAC3C,wCAAwC;QACxC,+CAA+C;QAC/C,4BAA4B;QAC5B,IACE,cAAc,CAAC,uBAAuB;YACtC,CAAC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC;gBAChC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;gBACpC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC;oBAChC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBACvC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC;oBAChC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBACvC,uBAAuB,CAAC,gBAAgB,CAAC;gBACzC,sBAAsB,CAAC,gBAAgB,CAAC,CAAC,EAC3C;YACA,cAAc,CAAC,uBAAuB,GAAG,KAAK,CAAC;SAChD;QACD,IAAI,CAAC,cAAc,CAAC,uBAAuB,EAAE;YAC3C,sBAAsB,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;SAC9C;IACH,CAAC,CAAC;IACF;;;;OAIG;IACH,MAAM,kBAAkB,GACtB,CACE,WAAqB,EACrB,cAAsB,EACtB,WAAiC,EACjC,mBAAkE,EAClE,8BAAwD,EACxD,oBAA0C,EACD,EAAE;QAC3C,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;;YACvC,MAAM,IAAI,GAAG,oBAAoB;gBAC/B,CAAC,CAAC,MAAA,MAAC,EAAwB,EAAC,2BAA2B,mDACnD,oBAAoB,EACpB,CAAC,CACF;gBACH,CAAC,CAAC,SAAS,CAAC;YACd,IAAI,EAAE,cAAc,EAAE,GAAG,EAAE,CAAC,iBAAiB,CAC3C,UAAU,EACV,cAAc,EACd,MAAM,CAAC,OAAO,EACd,IAAI,EACJ,qBAAqB,EACrB,mBAAmB,EACnB,IAAI,CACL,CAAC;YACF,IAAI,CAAC,cAAc,IAAI,OAAO,CAAC,8BAA8B,EAAE;gBAC7D,MAAM,YAAY,GAAG,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBACjD,MAAM,GAAG,GAAG,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACpE,IAAI,GAAG,EAAE;oBACP,MAAM,YAAY,GAAG,UAAU,CAAC,qBAAqB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;oBAC/D,KAAK,MAAM,cAAc,IAAI,YAAY,aAAZ,YAAY,cAAZ,YAAY,GAAI,EAAE,EAAE;wBAC/C,CAAC,EAAE,cAAc,EAAE,GAAG,EAAE,CAAC,iBAAiB,CACxC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,cAAc,EACjD,cAAc,EACd,MAAM,CAAC,OAAO,EACd,IAAI,EACJ,qBAAqB,EACrB,mBAAmB,EACnB,IAAI,CACL,CAAC,CAAC;wBACH,IAAI,cAAc;4BAAE,MAAM;qBAC3B;iBACF;aACF;YACD,IAAI,cAAc,EAAE;gBAClB,mBAAmB,CAAC,cAAc,CAAC,CAAC;aACrC;YACD,OAAO,cAAc,CAAC;QACxB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEJ,gFAAgF;IAChF,MAAM,mDAAmD,GACvD,CACE,UAAU,EACV,cAAc,EACd,cAA0E,EACZ,EAAE;QAChE,MAAM,GAAG,GAAG,EAAE,CAAC,0BAA0B,CACvC,UAAU,EACV,cAAc,EACd,qBAAqB,EACrB,cAAc,CACf,CAAC;QACF,IAAI,GAAG,IAAI,GAAG,CAAC,cAAc,EAAE;YAC7B,mBAAmB,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;SACzC;QACD,OAAO,GAAG,CAAC;IACb,CAAC,CAAC;IAEJ,MAAM,8BAA8B,GAClC,CACE,kBAAgE,EAChE,cAAsB,EACtB,mBAAkE,EAClE,OAAiC,EACjC,kBAAyE,CAAC,oDAAoD;MACrE,EAAE;QAC3D,8EAA8E;QAC9E,sDAAsD;QACtD,OAAO,kBAAkB,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,EAAE;YAClD,8BAA8B;YAC9B,MAAM,YAAY,GAAG,OAAO,iBAAiB,KAAK,QAAQ,CAAC;YAC3D,MAAM,IAAI,GAAG,YAAY;gBACvB,CAAC,CAAC,SAAS;gBACX,CAAC,CAAE,EAAwB,CAAC,uBAAwB,CAChD,iBAAiB,EACjB,kBAAkB,CACnB,CAAC;YACN,MAAM,OAAO,GAAG,YAAY;gBAC1B,CAAC,CAAC,iBAAiB;gBACnB,CAAC,CAAC,iBAAiB,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;YAC7C,IAAI,EAAE,8BAA8B,EAAE,GACpC,EAAE,CAAC,6BAA6B,CAC9B,OAAO,EACP,cAAc,EACd,MAAM,CAAC,OAAO,EACd,IAAI,EACJ,mBAAmB,EACnB,SAAS,EACT,IAAI,CACL,CAAC;YACJ,IAAI,iBAAiB,KAAK,MAAM,IAAI,CAAC,8BAA8B,EAAE;gBACnE,8HAA8H;gBAC9H,IAAI,wBAA4C,CAAC;gBACjD,IAAI;oBACF,wBAAwB,GAAG,yBAAyB,CAClD,0BAA0B,EAC1B,IAAI,CACL,CAAC;iBACH;gBAAC,MAAM,GAAE,CAAC,yEAAyE;gBACpF,IAAI,wBAAwB,EAAE;oBAC5B,MAAM,SAAS,GAAG,CAAC,IAAA,cAAO,EAAC,wBAAwB,EAAE,OAAO,CAAC,CAAC,CAAC;oBAC/D,CAAC,EAAE,8BAA8B,EAAE;wBACjC,EAAE,CAAC,6BAA6B,CAC9B,iBAAiB,EACjB,cAAc,EACd;4BACE,GAAG,MAAM,CAAC,OAAO;4BACjB,SAAS;yBACV,EACD,IAAI,EACJ,mBAAmB,CACpB,CAAC,CAAC;iBACN;aACF;YACD,IAAI,8BAA8B,EAAE;gBAClC,mBAAmB,CAAC,8BAA8B,CAAC,CAAC;aACrD;YACD,OAAO,8BAA8B,CAAC;QACxC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEJ,OAAO;QACL,kBAAkB;QAClB,mDAAmD;QACnD,8BAA8B;QAC9B,uBAAuB;QACvB,4BAA4B;KAC7B,CAAC;AACJ,CAAC;AAnOD,0DAmOC", "sourcesContent": ["import { resolve } from 'path';\nimport type { CreateOptions } from '.';\nimport type { Extensions } from './file-extensions';\nimport type { TSC<PERSON><PERSON>, TSInternal } from './ts-compiler-types';\nimport type { ProjectLocalResolveHelper } from './util';\n\n/**\n * @internal\n * In a factory because these are shared across both CompilerHost and LanguageService codepaths\n */\nexport function createResolverFunctions(kwargs: {\n  ts: TSCommon;\n  host: TSCommon.ModuleResolutionHost;\n  cwd: string;\n  getCanonicalFileName: (filename: string) => string;\n  config: TSCommon.ParsedCommandLine;\n  projectLocalResolveHelper: ProjectLocalResolveHelper;\n  options: CreateOptions;\n  extensions: Extensions;\n}) {\n  const {\n    host,\n    ts,\n    config,\n    cwd,\n    getCanonicalFileName,\n    projectLocalResolveHelper,\n    options,\n    extensions,\n  } = kwargs;\n  const moduleResolutionCache = ts.createModuleResolutionCache(\n    cwd,\n    getCanonicalFileName,\n    config.options\n  );\n  const knownInternalFilenames = new Set<string>();\n  /** \"Buckets\" (module directories) whose contents should be marked \"internal\" */\n  const internalBuckets = new Set<string>();\n\n  // Get bucket for a source filename.  Bucket is the containing `./node_modules/*/` directory\n  // For '/project/node_modules/foo/node_modules/bar/lib/index.js' bucket is '/project/node_modules/foo/node_modules/bar/'\n  // For '/project/node_modules/foo/node_modules/@scope/bar/lib/index.js' bucket is '/project/node_modules/foo/node_modules/@scope/bar/'\n  const moduleBucketRe = /.*\\/node_modules\\/(?:@[^\\/]+\\/)?[^\\/]+\\//;\n  function getModuleBucket(filename: string) {\n    const find = moduleBucketRe.exec(filename);\n    if (find) return find[0];\n    return '';\n  }\n\n  // Mark that this file and all siblings in its bucket should be \"internal\"\n  function markBucketOfFilenameInternal(filename: string) {\n    internalBuckets.add(getModuleBucket(filename));\n  }\n\n  function isFileInInternalBucket(filename: string) {\n    return internalBuckets.has(getModuleBucket(filename));\n  }\n\n  function isFileKnownToBeInternal(filename: string) {\n    return knownInternalFilenames.has(filename);\n  }\n\n  /**\n   * If we need to emit JS for a file, force TS to consider it non-external\n   */\n  const fixupResolvedModule = (\n    resolvedModule:\n      | TSCommon.ResolvedModule\n      | TSCommon.ResolvedTypeReferenceDirective\n  ) => {\n    const { resolvedFileName } = resolvedModule;\n    if (resolvedFileName === undefined) return;\n    // [MUST_UPDATE_FOR_NEW_FILE_EXTENSIONS]\n    // .ts,.mts,.cts is always switched to internal\n    // .js is switched on-demand\n    if (\n      resolvedModule.isExternalLibraryImport &&\n      ((resolvedFileName.endsWith('.ts') &&\n        !resolvedFileName.endsWith('.d.ts')) ||\n        (resolvedFileName.endsWith('.cts') &&\n          !resolvedFileName.endsWith('.d.cts')) ||\n        (resolvedFileName.endsWith('.mts') &&\n          !resolvedFileName.endsWith('.d.mts')) ||\n        isFileKnownToBeInternal(resolvedFileName) ||\n        isFileInInternalBucket(resolvedFileName))\n    ) {\n      resolvedModule.isExternalLibraryImport = false;\n    }\n    if (!resolvedModule.isExternalLibraryImport) {\n      knownInternalFilenames.add(resolvedFileName);\n    }\n  };\n  /*\n   * NOTE:\n   * Older ts versions do not pass `redirectedReference` nor `options`.\n   * We must pass `redirectedReference` to newer ts versions, but cannot rely on `options`, hence the weird argument name\n   */\n  const resolveModuleNames: TSCommon.LanguageServiceHost['resolveModuleNames'] =\n    (\n      moduleNames: string[],\n      containingFile: string,\n      reusedNames: string[] | undefined,\n      redirectedReference: TSCommon.ResolvedProjectReference | undefined,\n      optionsOnlyWithNewerTsVersions: TSCommon.CompilerOptions,\n      containingSourceFile?: TSCommon.SourceFile\n    ): (TSCommon.ResolvedModule | undefined)[] => {\n      return moduleNames.map((moduleName, i) => {\n        const mode = containingSourceFile\n          ? (ts as any as TSInternal).getModeForResolutionAtIndex?.(\n              containingSourceFile,\n              i\n            )\n          : undefined;\n        let { resolvedModule } = ts.resolveModuleName(\n          moduleName,\n          containingFile,\n          config.options,\n          host,\n          moduleResolutionCache,\n          redirectedReference,\n          mode\n        );\n        if (!resolvedModule && options.experimentalTsImportSpecifiers) {\n          const lastDotIndex = moduleName.lastIndexOf('.');\n          const ext = lastDotIndex >= 0 ? moduleName.slice(lastDotIndex) : '';\n          if (ext) {\n            const replacements = extensions.tsResolverEquivalents.get(ext);\n            for (const replacementExt of replacements ?? []) {\n              ({ resolvedModule } = ts.resolveModuleName(\n                moduleName.slice(0, -ext.length) + replacementExt,\n                containingFile,\n                config.options,\n                host,\n                moduleResolutionCache,\n                redirectedReference,\n                mode\n              ));\n              if (resolvedModule) break;\n            }\n          }\n        }\n        if (resolvedModule) {\n          fixupResolvedModule(resolvedModule);\n        }\n        return resolvedModule;\n      });\n    };\n\n  // language service never calls this, but TS docs recommend that we implement it\n  const getResolvedModuleWithFailedLookupLocationsFromCache: TSCommon.LanguageServiceHost['getResolvedModuleWithFailedLookupLocationsFromCache'] =\n    (\n      moduleName,\n      containingFile,\n      resolutionMode?: TSCommon.ModuleKind.CommonJS | TSCommon.ModuleKind.ESNext\n    ): TSCommon.ResolvedModuleWithFailedLookupLocations | undefined => {\n      const ret = ts.resolveModuleNameFromCache(\n        moduleName,\n        containingFile,\n        moduleResolutionCache,\n        resolutionMode\n      );\n      if (ret && ret.resolvedModule) {\n        fixupResolvedModule(ret.resolvedModule);\n      }\n      return ret;\n    };\n\n  const resolveTypeReferenceDirectives: TSCommon.LanguageServiceHost['resolveTypeReferenceDirectives'] =\n    (\n      typeDirectiveNames: string[] | readonly TSCommon.FileReference[],\n      containingFile: string,\n      redirectedReference: TSCommon.ResolvedProjectReference | undefined,\n      options: TSCommon.CompilerOptions,\n      containingFileMode?: TSCommon.SourceFile['impliedNodeFormat'] | undefined // new impliedNodeFormat is accepted by compilerHost\n    ): (TSCommon.ResolvedTypeReferenceDirective | undefined)[] => {\n      // Note: seems to be called with empty typeDirectiveNames array for all files.\n      // TODO consider using `ts.loadWithTypeDirectiveCache`\n      return typeDirectiveNames.map((typeDirectiveName) => {\n        // Copy-pasted from TS source:\n        const nameIsString = typeof typeDirectiveName === 'string';\n        const mode = nameIsString\n          ? undefined\n          : (ts as any as TSInternal).getModeForFileReference!(\n              typeDirectiveName,\n              containingFileMode\n            );\n        const strName = nameIsString\n          ? typeDirectiveName\n          : typeDirectiveName.fileName.toLowerCase();\n        let { resolvedTypeReferenceDirective } =\n          ts.resolveTypeReferenceDirective(\n            strName,\n            containingFile,\n            config.options,\n            host,\n            redirectedReference,\n            undefined,\n            mode\n          );\n        if (typeDirectiveName === 'node' && !resolvedTypeReferenceDirective) {\n          // Resolve @types/node relative to project first, then __dirname (copy logic from elsewhere / refactor into reusable function)\n          let typesNodePackageJsonPath: string | undefined;\n          try {\n            typesNodePackageJsonPath = projectLocalResolveHelper(\n              '@types/node/package.json',\n              true\n            );\n          } catch {} // gracefully do nothing when @types/node is not installed for any reason\n          if (typesNodePackageJsonPath) {\n            const typeRoots = [resolve(typesNodePackageJsonPath, '../..')];\n            ({ resolvedTypeReferenceDirective } =\n              ts.resolveTypeReferenceDirective(\n                typeDirectiveName,\n                containingFile,\n                {\n                  ...config.options,\n                  typeRoots,\n                },\n                host,\n                redirectedReference\n              ));\n          }\n        }\n        if (resolvedTypeReferenceDirective) {\n          fixupResolvedModule(resolvedTypeReferenceDirective);\n        }\n        return resolvedTypeReferenceDirective;\n      });\n    };\n\n  return {\n    resolveModuleNames,\n    getResolvedModuleWithFailedLookupLocationsFromCache,\n    resolveTypeReferenceDirectives,\n    isFileKnownToBeInternal,\n    markBucketOfFilenameInternal,\n  };\n}\n"]}