{"version": 3, "file": "cjs-resolve-hooks.js", "sourceRoot": "", "sources": ["../src/cjs-resolve-hooks.ts"], "names": [], "mappings": ";;;AAoBA;;GAEG;AACH,SAAgB,sCAAsC,CAAC,aAAsB;IAC3E,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAmC,CAAC;IACnE,MAAM,uBAAuB,GAAG,MAAM,CAAC,gBAAgB,CAAC;IACxD,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC;IAC1C,MAAM,iBAAiB,GAAG,aAAa,CAAC,OAAO,CAAC,oBAAoB,CAAC;IACrE,IAAI,iBAAiB,EAAE;QACrB,MAAM,EAAE,eAAe,EAAE,sBAAsB,EAAE,GAC/C,aAAa,CAAC,gBAAgB,EAAE,CAAC;QACnC,MAAM,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QAC3C,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC;QAC7B,SAAS,gBAAgB,CAEvB,OAAe,EACf,MAAe,EACf,MAAgB,EAChB,OAAsC,EACtC,GAAG,IAAQ;YAEX,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE;gBAC1B,OAAO,uBAAuB,CAAC,IAAI,CACjC,IAAI,EACJ,OAAO,EACP,MAAM,EACN,MAAM,EACN,OAAO,EACP,GAAG,IAAI,CACR,CAAC;YAEJ,OAAO,sBAAsB,CAAC,IAAI,CAChC,IAAI,EACJ,OAAO,EACP,MAAM,EACN,MAAM,EACN,OAAO,EACP,GAAG,IAAI,CACR,CAAC;QACJ,CAAC;QACD,SAAS,SAAS;YAChB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE;gBAC1B,OAAO,gBAAgB,CAAC,KAAK,CAAC,IAAI,EAAE,SAAgB,CAAC,CAAC;YACxD,OAAO,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE,SAAgB,CAAC,CAAC;QACvD,CAAC;KACF;AACH,CAAC;AA3CD,wFA2CC", "sourcesContent": ["import type Module = require('module');\nimport type { Service } from '.';\n\n/** @internal */\nexport type ModuleConstructorWithInternals = typeof Module & {\n  _resolveFilename(\n    request: string,\n    parent?: Module,\n    isMain?: boolean,\n    options?: ModuleResolveFilenameOptions,\n    ...rest: any[]\n  ): string;\n  _preloadModules(requests?: string[]): void;\n  _findPath(request: string, paths: string[], isMain: boolean): string;\n};\n\ninterface ModuleResolveFilenameOptions {\n  paths?: Array<string>;\n}\n\n/**\n * @internal\n */\nexport function installCommonjsResolveHooksIfNecessary(tsNodeService: Service) {\n  const Module = require('module') as ModuleConstructorWithInternals;\n  const originalResolveFilename = Module._resolveFilename;\n  const originalFindPath = Module._findPath;\n  const shouldInstallHook = tsNodeService.options.experimentalResolver;\n  if (shouldInstallHook) {\n    const { Module_findPath, Module_resolveFilename } =\n      tsNodeService.getNodeCjsLoader();\n    Module._resolveFilename = _resolveFilename;\n    Module._findPath = _findPath;\n    function _resolveFilename(\n      this: any,\n      request: string,\n      parent?: Module,\n      isMain?: boolean,\n      options?: ModuleResolveFilenameOptions,\n      ...rest: []\n    ): string {\n      if (!tsNodeService.enabled())\n        return originalResolveFilename.call(\n          this,\n          request,\n          parent,\n          isMain,\n          options,\n          ...rest\n        );\n\n      return Module_resolveFilename.call(\n        this,\n        request,\n        parent,\n        isMain,\n        options,\n        ...rest\n      );\n    }\n    function _findPath(this: any): string {\n      if (!tsNodeService.enabled())\n        return originalFindPath.apply(this, arguments as any);\n      return Module_findPath.apply(this, arguments as any);\n    }\n  }\n}\n"]}