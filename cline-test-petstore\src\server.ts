
import dotenv from 'dotenv';
import * as path from 'path';
import fs from 'fs';

// Try to load .env from both root and dist (for manual runs)
const envPathRoot = path.resolve(__dirname, '../.env');
const envPathDist = path.resolve(__dirname, '../../.env');
if (fs.existsSync(envPathRoot)) {
  dotenv.config({ path: envPathRoot });
} else if (fs.existsSync(envPathDist)) {
  dotenv.config({ path: envPathDist });
} else {
  dotenv.config();
}

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import axios from 'axios';
import { router as apiRoutes } from './routes';

const app = express();
const PORT = process.env.PORT || 8000;
const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';

// Security middleware
app.use(helmet());
app.use(cors());

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check
app.get('/health', (req: express.Request, res: express.Response) => {
  res.json({
    status: 'healthy',
    name: 'swagger-petstore-openapi-3-0',
    version: '1.0.26',
    timestamp: new Date().toISOString(),
    baseUrl: BASE_URL
  });
});

// MCP tool endpoints
app.use('/tools', apiRoutes);

// MCP Protocol Types
interface MCPRequest {
  jsonrpc: '2.0';
  id?: string | number;
  method: string;
  params?: any;
}

interface MCPResponse {
  jsonrpc: '2.0';
  id?: string | number;
  result?: any;
  error?: {
    code: number;
    message: string;
    data?: any;
  };
}

// MCP Server Info
const SERVER_INFO = {
  name: 'swagger-petstore-openapi-3-0',
  version: '1.0.26',
  description: 'Generated MCP server from OpenAPI specification',
  author: 'openapi-to-mcp'
};

// Available tools from OpenAPI spec
const AVAILABLE_TOOLS = [
  {
    "name": "addPet",
    "description": "Add a new pet to the store.",
    "inputSchema": {
      "type": "object",
      "properties": {
        "body": {
          "type": "object",
          "description": "Create a new pet in the store"
        }
      },
      "required": [
        "body"
      ]
    }
  },
  {
    "name": "updatePet",
    "description": "Update an existing pet.",
    "inputSchema": {
      "type": "object",
      "properties": {
        "body": {
          "type": "object",
          "description": "Update an existent pet in the store"
        }
      },
      "required": [
        "body"
      ]
    }
  },
  {
    "name": "findPetsByStatus",
    "description": "Finds Pets by status.",
    "inputSchema": {
      "type": "object",
      "properties": {
        "query": {
          "type": "object",
          "description": "Query parameters",
          "properties": {
            "status": {
              "type": "string",
              "description": "Status values that need to be considered for filter"
            }
          }
        }
      },
      "required": []
    }
  },
  {
    "name": "findPetsByTags",
    "description": "Finds Pets by tags.",
    "inputSchema": {
      "type": "object",
      "properties": {
        "query": {
          "type": "object",
          "description": "Query parameters",
          "properties": {
            "tags": {
              "type": "array",
              "description": "Tags to filter by"
            }
          }
        }
      },
      "required": []
    }
  },
  {
    "name": "getPetById",
    "description": "Find pet by ID.",
    "inputSchema": {
      "type": "object",
      "properties": {
        "petId": {
          "type": "integer",
          "description": "ID of pet to return"
        }
      },
      "required": [
        "petId"
      ]
    }
  },
  {
    "name": "updatePetWithForm",
    "description": "Updates a pet in the store with form data.",
    "inputSchema": {
      "type": "object",
      "properties": {
        "petId": {
          "type": "integer",
          "description": "ID of pet that needs to be updated"
        },
        "query": {
          "type": "object",
          "description": "Query parameters",
          "properties": {
            "name": {
              "type": "string",
              "description": "Name of pet that needs to be updated"
            },
            "status": {
              "type": "string",
              "description": "Status of pet that needs to be updated"
            }
          }
        }
      },
      "required": [
        "petId"
      ]
    }
  },
  {
    "name": "deletePet",
    "description": "Deletes a pet.",
    "inputSchema": {
      "type": "object",
      "properties": {
        "petId": {
          "type": "integer",
          "description": "Pet id to delete"
        },
        "headers": {
          "type": "object",
          "description": "HTTP headers",
          "properties": {
            "api_key": {
              "type": "string",
              "description": "Header: api_key"
            }
          }
        }
      },
      "required": [
        "petId"
      ]
    }
  },
  {
    "name": "uploadFile",
    "description": "Uploads an image.",
    "inputSchema": {
      "type": "object",
      "properties": {
        "petId": {
          "type": "integer",
          "description": "ID of pet to update"
        },
        "query": {
          "type": "object",
          "description": "Query parameters",
          "properties": {
            "additionalMetadata": {
              "type": "string",
              "description": "Additional Metadata"
            }
          }
        },
        "body": {
          "type": "object",
          "description": "Request body"
        }
      },
      "required": [
        "petId"
      ]
    }
  },
  {
    "name": "getInventory",
    "description": "Returns pet inventories by status.",
    "inputSchema": {
      "type": "object",
      "properties": {},
      "required": []
    }
  },
  {
    "name": "placeOrder",
    "description": "Place an order for a pet.",
    "inputSchema": {
      "type": "object",
      "properties": {
        "body": {
          "type": "object",
          "description": "Request body"
        }
      },
      "required": []
    }
  },
  {
    "name": "getOrderById",
    "description": "Find purchase order by ID.",
    "inputSchema": {
      "type": "object",
      "properties": {
        "orderId": {
          "type": "integer",
          "description": "ID of order that needs to be fetched"
        }
      },
      "required": [
        "orderId"
      ]
    }
  },
  {
    "name": "deleteOrder",
    "description": "Delete purchase order by identifier.",
    "inputSchema": {
      "type": "object",
      "properties": {
        "orderId": {
          "type": "integer",
          "description": "ID of the order that needs to be deleted"
        }
      },
      "required": [
        "orderId"
      ]
    }
  },
  {
    "name": "createUser",
    "description": "Create user.",
    "inputSchema": {
      "type": "object",
      "properties": {
        "body": {
          "type": "object",
          "description": "Created user object"
        }
      },
      "required": []
    }
  },
  {
    "name": "createUsersWithListInput",
    "description": "Creates list of users with given input array.",
    "inputSchema": {
      "type": "object",
      "properties": {
        "body": {
          "type": "object",
          "description": "Request body"
        }
      },
      "required": []
    }
  },
  {
    "name": "loginUser",
    "description": "Logs user into the system.",
    "inputSchema": {
      "type": "object",
      "properties": {
        "query": {
          "type": "object",
          "description": "Query parameters",
          "properties": {
            "username": {
              "type": "string",
              "description": "The user name for login"
            },
            "password": {
              "type": "string",
              "description": "The password for login in clear text"
            }
          }
        }
      },
      "required": []
    }
  },
  {
    "name": "logoutUser",
    "description": "Logs out current logged in user session.",
    "inputSchema": {
      "type": "object",
      "properties": {},
      "required": []
    }
  },
  {
    "name": "getUserByName",
    "description": "Get user by user name.",
    "inputSchema": {
      "type": "object",
      "properties": {
        "username": {
          "type": "string",
          "description": "The name that needs to be fetched. Use user1 for testing"
        }
      },
      "required": [
        "username"
      ]
    }
  },
  {
    "name": "updateUser",
    "description": "Update user resource.",
    "inputSchema": {
      "type": "object",
      "properties": {
        "username": {
          "type": "string",
          "description": "name that need to be deleted"
        },
        "body": {
          "type": "object",
          "description": "Update an existent user in the store"
        }
      },
      "required": [
        "username"
      ]
    }
  },
  {
    "name": "deleteUser",
    "description": "Delete user resource.",
    "inputSchema": {
      "type": "object",
      "properties": {
        "username": {
          "type": "string",
          "description": "The name that needs to be deleted"
        }
      },
      "required": [
        "username"
      ]
    }
  }
];

// MCP Protocol Handler - supports both stdio and HTTP
app.post('/mcp', async (req: express.Request, res: express.Response) => {
  try {
    const request: MCPRequest = req.body;

    // Validate JSON-RPC 2.0 format
    if (request.jsonrpc !== '2.0') {
      return res.status(400).json({
        jsonrpc: '2.0',
        id: request.id,
        error: {
          code: -32600,
          message: 'Invalid Request - must be JSON-RPC 2.0'
        }
      });
    }

    const response = await handleMCPRequest(request);
    res.json(response);
  } catch (error: any) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    res.status(500).json({
      jsonrpc: '2.0',
      id: req.body?.id,
      error: {
        code: -32603,
        message: 'Internal error',
        data: errorMessage
      }
    });
  }
});

// SSE endpoint for streaming MCP protocol
app.get('/mcp/sse', (req: express.Request, res: express.Response) => {
  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Cache-Control'
  });

  // Send initial connection event
  res.write('data: {"jsonrpc":"2.0","method":"connection","params":{"status":"connected"}}\n\n');

  // Handle client disconnect
  req.on('close', () => {
    res.end();
  });
});

// MCP Request Handler
async function handleMCPRequest(request: MCPRequest): Promise<MCPResponse> {
  const { method, params, id } = request;

  try {
    switch (method) {
      case 'initialize':
        return {
          jsonrpc: '2.0',
          id,
          result: {
            protocolVersion: '2024-11-05',
            capabilities: {
              tools: {},
              logging: {},
              prompts: {},
              resources: {}
            },
            serverInfo: SERVER_INFO
          }
        };

      case 'tools/list':
        return {
          jsonrpc: '2.0',
          id,
          result: {
            tools: AVAILABLE_TOOLS
          }
        };

      case 'tools/call':
        const { name: toolName, arguments: toolArgs } = params;

        // Validate tool exists
        const tool = AVAILABLE_TOOLS.find((t: any) => t.name === toolName);
        if (!tool) {
          return {
            jsonrpc: '2.0',
            id,
            error: {
              code: -32602,
              message: `Tool '${toolName}' not found`
            }
          };
        }

        // Call the tool
        const toolResult = await callTool(toolName, toolArgs);

        return {
          jsonrpc: '2.0',
          id,
          result: {
            content: [
              {
                type: 'text',
                text: JSON.stringify(toolResult, null, 2)
              }
            ]
          }
        };

      case 'ping':
        return {
          jsonrpc: '2.0',
          id,
          result: {}
        };

      default:
        return {
          jsonrpc: '2.0',
          id,
          error: {
            code: -32601,
            message: `Method '${method}' not found`
          }
        };
    }
  } catch (error: any) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return {
      jsonrpc: '2.0',
      id,
      error: {
        code: -32603,
        message: 'Internal error',
        data: errorMessage
      }
    };
  }
}

// Tool execution function
async function callTool(toolName: string, args: any): Promise<any> {
  try {
    const response = await fetch(`http://localhost:${PORT}/tools/${toolName}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(args)
    });

    if (!response.ok) {
      throw new Error(`Tool call failed: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error: any) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new Error(`Failed to execute tool '${toolName}': ${errorMessage}`);
  }
}

// MCP Tools endpoint - lists available tools for MCP clients
app.get('/tools', (req: express.Request, res: express.Response) => {
  try {
    // Load tools from types file if available
    let openApiTools: any[] = [];
    try {
      openApiTools = require('./types').openApiTools || [];
    } catch (error) {
      console.log('[MCP] No types file found, returning empty tools list');
    }

    res.json({
      tools: openApiTools,
      count: openApiTools.length,
      server: {
        name: 'swagger-petstore-openapi-3-0',
        version: '1.0.26',
        baseUrl: BASE_URL
      }
    });
  } catch (error: any) {
    console.error('[MCP] Error loading tools:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    res.status(500).json({
      error: 'Failed to load tools',
      details: errorMessage
    });
  }
});

// Error handling
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Server error:', error);
  res.status(500).json({
    error: 'Internal server error',
    message: error instanceof Error ? error.message : String(error)
  });
});

// 404 handler
app.use('*', (req: express.Request, res: express.Response) => {
  res.status(404).json({
    error: 'Not found',
    message: `Route ${req.originalUrl} not found`
  });
});

// Check if running in stdio mode (for MCP clients like Cline)
// Multiple detection methods for better reliability
const isStdioMode = (
  process.argv.includes('--stdio') ||  // Explicit flag
  process.env.MCP_STDIO_MODE === 'true' ||  // Environment variable
  (process.stdin.isTTY === false && process.stdout.isTTY === false)  // Both stdin and stdout are piped
);

if (isStdioMode) {
  // STDIO MODE: Handle MCP protocol via stdin/stdout
  console.error(`🔌 MCP Server starting in stdio mode for ${AVAILABLE_TOOLS.length} tools`);
  console.error(`🔧 Detection: stdin.isTTY=${process.stdin.isTTY}, stdout.isTTY=${process.stdout.isTTY}`);

  // Handle stdio for MCP clients (Cline, Cursor, Windsurf, etc.)
  let buffer = '';

  // Set stdin to raw mode for better handling
  if (process.stdin.setRawMode) {
    process.stdin.setRawMode(false);
  }
  process.stdin.setEncoding('utf8');

  process.stdin.on('data', async (chunk) => {
    try {
      buffer += chunk.toString();

      // Process complete JSON messages (handle both \n and \r\n)
      const lines = buffer.split(/\r?\n/);
      buffer = lines.pop() || '';

      for (const line of lines) {
        const trimmedLine = line.trim();
        if (trimmedLine) {
          console.error(`📥 Received: ${trimmedLine.substring(0, 100)}...`);

          try {
            const request: MCPRequest = JSON.parse(trimmedLine);
            const response = await handleMCPRequest(request);
            const responseStr = JSON.stringify(response);

            console.error(`📤 Sending: ${responseStr.substring(0, 100)}...`);
            process.stdout.write(responseStr + '\n');

          } catch (parseError) {
            const errorMessage = parseError instanceof Error ? parseError.message : String(parseError);
            console.error(`❌ Parse error: ${errorMessage}`);
            const errorResponse: MCPResponse = {
              jsonrpc: '2.0',
              error: {
                code: -32700,
                message: 'Parse error',
                data: errorMessage
              }
            };
            process.stdout.write(JSON.stringify(errorResponse) + '\n');
          }
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`❌ Stdin error: ${errorMessage}`);
    }
  });

  process.stdin.on('end', () => {
    process.exit(0);
  });

} else {
  // HTTP MODE: Start HTTP server for web-based clients
  const server = app.listen(PORT, () => {
    console.log(`🚀 swagger-petstore-openapi-3-0 MCP Server running on port ${PORT}`);
    console.log(`📋 Health check: http://localhost:${PORT}/health`);
    console.log(`🔧 MCP endpoint: http://localhost:${PORT}/mcp`);
    console.log(`📡 SSE endpoint: http://localhost:${PORT}/mcp/sse`);
    console.log(`🛠️  Tools endpoint: http://localhost:${PORT}/tools`);
    console.log(`🌐 Base URL: ${BASE_URL}`);
    console.log(``);
    console.log(`📖 Available tools: ${AVAILABLE_TOOLS.length}`);
    AVAILABLE_TOOLS.forEach((tool: any) => {
      console.log(`   - ${tool.name}: ${tool.description}`);
    });
    console.log(``);
    console.log(`🔌 MCP Client Configuration:`);
    console.log(`   stdio: node ${__filename.replace('.ts', '.js')}`);
    console.log(`   http: POST http://localhost:${PORT}/mcp`);
    console.log(`   sse: GET http://localhost:${PORT}/mcp/sse`);
  });

  // Graceful shutdown for HTTP mode
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down MCP server...');
    server.close(() => {
      process.exit(0);
    });
  });

  process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down MCP server...');
    server.close(() => {
      process.exit(0);
    });
  });
}

export default app;
