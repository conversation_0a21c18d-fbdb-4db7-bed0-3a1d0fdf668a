// Quick test to generate an MCP server with the new protocol implementation
const fetch = require('node-fetch');

async function testMCPGeneration() {
  try {
    console.log('🧪 Testing MCP server generation...');
    
    const response = await fetch('http://localhost:3000/api/convert', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        openapi: 'https://petstore3.swagger.io/api/v3/openapi.json',
        config: {
          name: 'test-mcp-server',
          version: '1.0.0',
          description: 'Test MCP server with full protocol support'
        }
      })
    });

    if (!response.ok) {
      throw new Error(`Generation failed: ${response.status}`);
    }

    const result = await response.json();
    console.log('✅ MCP server generated successfully!');
    console.log('📦 Bundle ID:', result.bundleId);
    console.log('📊 Stats:', result.stats);
    console.log('⬇️  Download URL:', result.downloadUrl);
    
    return result;
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    throw error;
  }
}

// Run test if this file is executed directly
if (require.main === module) {
  testMCPGeneration()
    .then(() => console.log('🎉 Test completed successfully!'))
    .catch(() => process.exit(1));
}

module.exports = { testMCPGeneration };
