{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst ListMusic = createLucideIcon(\"ListMusic\", [[\"path\", {\n  d: \"M21 15V6\",\n  key: \"h1cx4g\"\n}], [\"path\", {\n  d: \"M18.5 18a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z\",\n  key: \"8saifv\"\n}], [\"path\", {\n  d: \"M12 12H3\",\n  key: \"18klou\"\n}], [\"path\", {\n  d: \"M16 6H3\",\n  key: \"1wxfjs\"\n}], [\"path\", {\n  d: \"M12 18H3\",\n  key: \"11ftsu\"\n}]]);\nexport { ListMusic as default };", "map": {"version": 3, "names": ["ListMusic", "createLucideIcon", "d", "key"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\list-music.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ListMusic\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTVWNiIgLz4KICA8cGF0aCBkPSJNMTguNSAxOGEyLjUgMi41IDAgMSAwIDAtNSAyLjUgMi41IDAgMCAwIDAgNVoiIC8+CiAgPHBhdGggZD0iTTEyIDEySDMiIC8+CiAgPHBhdGggZD0iTTE2IDZIMyIgLz4KICA8cGF0aCBkPSJNMTIgMThIMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/list-music\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ListMusic = createLucideIcon('ListMusic', [\n  ['path', { d: 'M21 15V6', key: 'h1cx4g' }],\n  [\n    'path',\n    { d: 'M18.5 18a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z', key: '8saifv' },\n  ],\n  ['path', { d: 'M12 12H3', key: '18klou' }],\n  ['path', { d: 'M16 6H3', key: '1wxfjs' }],\n  ['path', { d: 'M12 18H3', key: '11ftsu' }],\n]);\n\nexport default ListMusic;\n"], "mappings": ";;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CACE,QACA;EAAED,CAAA,EAAG,+CAAiD;EAAAC,GAAA,EAAK;AAAS,EACtE,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}