{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Sandwich = createLucideIcon(\"Sandwich\", [[\"path\", {\n  d: \"M3 11v3a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1v-3\",\n  key: \"34v9d7\"\n}], [\"path\", {\n  d: \"M12 19H4a1 1 0 0 1-1-1v-2a1 1 0 0 1 1-1h16a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-3.83\",\n  key: \"1k5vfb\"\n}], [\"path\", {\n  d: \"m3 11 7.77-6.04a2 2 0 0 1 2.46 0L21 11H3Z\",\n  key: \"1oe7l6\"\n}], [\"path\", {\n  d: \"M12.97 19.77 7 15h12.5l-3.75 4.5a2 2 0 0 1-2.78.27Z\",\n  key: \"1ts2ri\"\n}]]);\nexport { Sandwich as default };", "map": {"version": 3, "names": ["Sandwich", "createLucideIcon", "d", "key"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\sandwich.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Sandwich\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxMXYzYTEgMSAwIDAgMCAxIDFoMTZhMSAxIDAgMCAwIDEtMXYtMyIgLz4KICA8cGF0aCBkPSJNMTIgMTlINGExIDEgMCAwIDEtMS0xdi0yYTEgMSAwIDAgMSAxLTFoMTZhMSAxIDAgMCAxIDEgMXYyYTEgMSAwIDAgMS0xIDFoLTMuODMiIC8+CiAgPHBhdGggZD0ibTMgMTEgNy43Ny02LjA0YTIgMiAwIDAgMSAyLjQ2IDBMMjEgMTFIM1oiIC8+CiAgPHBhdGggZD0iTTEyLjk3IDE5Ljc3IDcgMTVoMTIuNWwtMy43NSA0LjVhMiAyIDAgMCAxLTIuNzguMjdaIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/sandwich\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Sandwich = createLucideIcon('Sandwich', [\n  ['path', { d: 'M3 11v3a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1v-3', key: '34v9d7' }],\n  [\n    'path',\n    {\n      d: 'M12 19H4a1 1 0 0 1-1-1v-2a1 1 0 0 1 1-1h16a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-3.83',\n      key: '1k5vfb',\n    },\n  ],\n  ['path', { d: 'm3 11 7.77-6.04a2 2 0 0 1 2.46 0L21 11H3Z', key: '1oe7l6' }],\n  [\n    'path',\n    { d: 'M12.97 19.77 7 15h12.5l-3.75 4.5a2 2 0 0 1-2.78.27Z', key: '1ts2ri' },\n  ],\n]);\n\nexport default Sandwich;\n"], "mappings": ";;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,2CAA6C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1E,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,2CAA6C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1E,CACE,QACA;EAAED,CAAA,EAAG,qDAAuD;EAAAC,GAAA,EAAK;AAAS,EAC5E,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}