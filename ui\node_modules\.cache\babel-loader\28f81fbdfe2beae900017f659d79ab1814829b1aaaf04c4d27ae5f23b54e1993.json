{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Speaker = createLucideIcon(\"Speaker\", [[\"rect\", {\n  width: \"16\",\n  height: \"20\",\n  x: \"4\",\n  y: \"2\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"76otgf\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"14\",\n  r: \"4\",\n  key: \"1jruaj\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12.01\",\n  y1: \"6\",\n  y2: \"6\",\n  key: \"16cbga\"\n}]]);\nexport { Speaker as default };", "map": {"version": 3, "names": ["Speaker", "createLucideIcon", "width", "height", "x", "y", "rx", "ry", "key", "cx", "cy", "r", "x1", "x2", "y1", "y2"], "sources": ["D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\node_modules\\lucide-react\\src\\icons\\speaker.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Speaker\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTYiIGhlaWdodD0iMjAiIHg9IjQiIHk9IjIiIHJ4PSIyIiByeT0iMiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjE0IiByPSI0IiAvPgogIDxsaW5lIHgxPSIxMiIgeDI9IjEyLjAxIiB5MT0iNiIgeTI9IjYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/speaker\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Speaker = createLucideIcon('Speaker', [\n  [\n    'rect',\n    {\n      width: '16',\n      height: '20',\n      x: '4',\n      y: '2',\n      rx: '2',\n      ry: '2',\n      key: '76otgf',\n    },\n  ],\n  ['circle', { cx: '12', cy: '14', r: '4', key: '1jruaj' }],\n  ['line', { x1: '12', x2: '12.01', y1: '6', y2: '6', key: '16cbga' }],\n]);\n\nexport default Speaker;\n"], "mappings": ";;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CACE,QACA;EACEC,KAAO;EACPC,MAAQ;EACRC,CAAG;EACHC,CAAG;EACHC,EAAI;EACJC,EAAI;EACJC,GAAK;AACP,EACF,EACA,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACxD,CAAC,QAAQ;EAAEI,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,OAAS;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAP,GAAA,EAAK;AAAA,CAAU,EACpE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}