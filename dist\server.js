"use strict";
/**
 * API server entry point
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const path = __importStar(require("path"));
// axios removed - no longer needed for LLM calls
const convert_1 = require("./routes/convert");
const download_1 = require("./routes/download");
const errorHandler_1 = require("./middleware/errorHandler");
const instantMcp_1 = __importDefault(require("./routes/instantMcp"));
const servers_1 = require("./routes/servers");
console.log('🚀 Starting MCPify server...');
const app = (0, express_1.default)();
const PORT = process.env.PORT || 3000;
console.log('📦 Express app created');
console.log('🔧 Port:', PORT);
// Security middleware
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)({
    origin: process.env.CORS_ORIGIN || '*',
    methods: ['GET', 'POST', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));
// Body parsing middleware
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
// Serve static files from UI build (only if directory exists)
const uiBuildPath = path.join(__dirname, '../ui/build');
try {
    if (require('fs').existsSync(uiBuildPath)) {
        app.use(express_1.default.static(uiBuildPath));
        console.log('✅ Serving UI from:', uiBuildPath);
    }
    else {
        console.log('⚠️ UI build directory not found:', uiBuildPath);
    }
}
catch (error) {
    console.log('⚠️ Error setting up static files:', error instanceof Error ? error.message : String(error));
}
// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: process.env.npm_package_version || '1.0.0',
        uptime: process.uptime()
    });
});
// API routes
console.log('🔗 Setting up routes...');
try {
    app.use('/api/convert', convert_1.convertRoutes);
    console.log('✅ Convert routes loaded');
    app.use('/api/download', download_1.downloadRoutes);
    console.log('✅ Download routes loaded');
    app.use('/api/instant-mcp', instantMcp_1.default);
    console.log('✅ Instant MCP routes loaded');
    app.use('/api/servers', servers_1.serversRoutes);
    console.log('✅ Servers routes loaded');
}
catch (error) {
    console.error('❌ Error setting up routes:', error);
}
// No chat endpoint - this is a pure MCP server generator
// API info endpoint
app.get('/api', (req, res) => {
    res.json({
        name: 'MCPify - OpenAPI to MCP Server Generator',
        version: process.env.npm_package_version || '1.0.0',
        description: 'Convert OpenAPI specifications to MCP servers',
        endpoints: {
            '/api/health': 'GET - Health check',
            '/api/convert': 'POST - Convert OpenAPI to MCP',
            '/api/download/:bundleId': 'GET - Download generated bundle'
        },
        documentation: 'https://mcpify.com/docs'
    });
});
// Error handling middleware (must be last)
app.use(errorHandler_1.errorHandler);
// Serve React app for all non-API routes (SPA fallback)
app.get('*', (req, res) => {
    if (!req.path.startsWith('/api/')) {
        res.sendFile(path.join(__dirname, '../ui/build/index.html'));
    }
    else {
        res.status(404).json({
            error: 'Not Found',
            message: `Route ${req.originalUrl} not found`,
            availableEndpoints: [
                'GET /api',
                'GET /api/health',
                'POST /api/convert',
                'GET /api/download/:bundleId'
            ]
        });
    }
});
// Start server
const server = app.listen(PORT, () => {
    console.log(`🚀 MCPify Server running on port ${PORT}`);
    console.log(`🌐 Web Interface: http://localhost:${PORT}/`);
    console.log(`📖 API Documentation: http://localhost:${PORT}/api`);
    console.log(`🔧 Health Check: http://localhost:${PORT}/api/health`);
    console.log(`⚡ Convert APIs to MCP servers at: http://localhost:${PORT}/convert`);
});
// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('🛑 SIGTERM received, shutting down gracefully');
    server.close(() => {
        console.log('✅ Server closed');
        process.exit(0);
    });
});
process.on('SIGINT', () => {
    console.log('🛑 SIGINT received, shutting down gracefully');
    server.close(() => {
        console.log('✅ Server closed');
        process.exit(0);
    });
});
// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
    process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
exports.default = app;
//# sourceMappingURL=server.js.map