#!/usr/bin/env node

/**
 * Test the API routes directly to debug the issue
 */

const axios = require('axios');

async function testDirectAPI() {
  console.log('🧪 Testing Direct API Calls...\n');

  // Test 1: Direct call to Petstore API
  console.log('🔗 Test 1: Direct Petstore API call');
  try {
    const response = await axios.get('https://petstore3.swagger.io/api/v3/pet/findByStatus?status=available');
    console.log('✅ Direct API Success:', response.data.length, 'pets found');
    console.log('📦 Sample pet:', response.data[0]?.name || 'No pets');
  } catch (error) {
    console.log('❌ Direct API Error:', error.message);
  }

  // Test 2: Test our server's route directly (HTTP mode)
  console.log('\n🔗 Test 2: Our server route (HTTP mode)');
  
  // Start our server in HTTP mode
  const { spawn } = require('child_process');
  const serverPath = './cline-test-petstore/dist/server.js';
  
  const server = spawn('node', [serverPath], {
    stdio: ['pipe', 'pipe', 'pipe'],
    env: { 
      ...process.env, 
      PORT: '8002',
      BASE_URL: 'https://petstore3.swagger.io/api/v3'
    }
  });

  server.stderr.on('data', (data) => {
    console.log('🔍 Server Log:', data.toString().trim());
  });

  // Wait for server to start
  await new Promise(resolve => setTimeout(resolve, 3000));

  try {
    // Test the route endpoint
    const response = await axios.post('http://localhost:8002/tools/findPetsByStatus', {
      query: { status: 'available' }
    });
    console.log('✅ Route Success:', response.data.success);
    console.log('📦 Data:', response.data.data?.length || 0, 'pets found');
  } catch (error) {
    console.log('❌ Route Error:', error.response?.data || error.message);
  }

  // Test 3: Test MCP tool call
  console.log('\n🔗 Test 3: MCP tool call');
  try {
    const response = await axios.post('http://localhost:8002/mcp', {
      jsonrpc: '2.0',
      id: 1,
      method: 'tools/call',
      params: {
        name: 'findPetsByStatus',
        arguments: {
          query: { status: 'available' }
        }
      }
    });
    console.log('✅ MCP Success:', response.data.result ? 'Has result' : 'No result');
    console.log('📦 Response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.log('❌ MCP Error:', error.response?.data || error.message);
  }

  // Cleanup
  server.kill();
  console.log('\n🛑 Server stopped');
}

testDirectAPI().catch(console.error);
